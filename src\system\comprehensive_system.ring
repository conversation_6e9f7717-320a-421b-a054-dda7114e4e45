/*
=============================================================================
النظام الشامل للطرق - حل مشاكل التواصل بين JavaScript و Ring
Ring WebView IDE - Comprehensive Method System
=============================================================================
*/

# Load required files
load "../core/config.ring"
load "jsonlib.ring"

/*
=============================================================================
كلاس ComprehensiveSystem - النظام الشامل للطرق
Class: ComprehensiveSystem
Description: Comprehensive method system for JavaScript-Ring communication
=============================================================================
*/
class ComprehensiveSystem
    
    # Private properties
    private
        oWebView = NULL
        aHandlers = []
        aMethodRegistry = []
        bInitialized = false
        
    # Public properties
    cSystemName = "Ring WebView IDE System"
    nVersion = 1.0
    
    /*
    =========================================================================
    دالة البناء
    Function: init
    Description: Initialize comprehensive system
    Input: oWebViewInstance (Object) - WebView wrapper instance
    =========================================================================
    */
    func init oWebViewInstance
        if oWebViewInstance = NULL
            raise("يجب تمرير مثيل WebView صحيح")
        ok
        
        self.oWebView = oWebViewInstance
        self.initializeHandlers()
        self.registerCoreMethods()
        self.bInitialized = true
        
        ? "تم تهيئة النظام الشامل للطرق بنجاح"
        
    /*
    =========================================================================
    دالة تهيئة المعالجات
    Function: initializeHandlers
    Description: Initialize all system handlers
    =========================================================================
    */
    func initializeHandlers
        # Load and initialize handlers
        load "file_handler.ring"
        load "code_handler.ring" 
        load "project_handler.ring"
        load "system_handler.ring"
        
        # Create handler instances
        self.aHandlers = [
            :file = new ComprehensiveFileHandler(self.oWebView),
            :code = new ComprehensiveCodeHandler(self.oWebView),
            :project = new ComprehensiveProjectHandler(self.oWebView),
            :system = new ComprehensiveSystemHandler(self.oWebView)
        ]
        
    /*
    =========================================================================
    دالة تسجيل الطرق الأساسية
    Function: registerCoreMethods
    Description: Register core system methods
    =========================================================================
    */
    func registerCoreMethods
        # File operations
        self.registerMethod("file_read", "handleFileRead")
        self.registerMethod("file_write", "handleFileWrite")
        self.registerMethod("file_delete", "handleFileDelete")
        self.registerMethod("file_exists", "handleFileExists")
        self.registerMethod("file_list", "handleFileList")
        
        # Code operations
        self.registerMethod("code_execute", "handleCodeExecute")
        self.registerMethod("code_validate", "handleCodeValidate")
        self.registerMethod("code_format", "handleCodeFormat")
        
        # Project operations
        self.registerMethod("project_create", "handleProjectCreate")
        self.registerMethod("project_open", "handleProjectOpen")
        self.registerMethod("project_save", "handleProjectSave")
        
        # System operations
        self.registerMethod("system_info", "handleSystemInfo")
        self.registerMethod("system_command", "handleSystemCommand")
        
    /*
    =========================================================================
    دالة تسجيل طريقة جديدة
    Function: registerMethod
    Description: Register a new method
    Input: cMethodName (String), cHandlerFunc (String)
    =========================================================================
    */
    func registerMethod cMethodName, cHandlerFunc
        # Add to registry
        self.aMethodRegistry + [cMethodName, cHandlerFunc]
        
        # Bind to WebView
        if self.oWebView != NULL
            self.oWebView.bindFunction(cMethodName, cHandlerFunc)
        ok
        
    /*
    =========================================================================
    دالة معالجة قراءة الملف
    Function: handleFileRead
    Description: Handle file read operation
    Input: nId (Number), cRequest (String)
    =========================================================================
    */
    func handleFileRead nId, cRequest
        try
            aParams = json2list(cRequest)
            cFilePath = aParams[1]
            
            cResult = self.aHandlers[:file].readFile(cFilePath)
            cResponse = list2json([cResult])
            
            self.oWebView.oWebView.wreturn(nId, WEBVIEW_ERROR_OK, cResponse)
            
        catch
            cError = list2json(["خطأ في قراءة الملف: " + cCatchError])
            self.oWebView.oWebView.wreturn(nId, WEBVIEW_ERROR_UNSPECIFIED, cError)
        done
        
    /*
    =========================================================================
    دالة معالجة كتابة الملف
    Function: handleFileWrite
    Description: Handle file write operation
    Input: nId (Number), cRequest (String)
    =========================================================================
    */
    func handleFileWrite nId, cRequest
        try
            aParams = json2list(cRequest)
            cFilePath = aParams[1]
            cContent = aParams[2]
            
            bResult = self.aHandlers[:file].writeFile(cFilePath, cContent)
            cResponse = list2json([bResult])
            
            self.oWebView.oWebView.wreturn(nId, WEBVIEW_ERROR_OK, cResponse)
            
        catch
            cError = list2json(["خطأ في كتابة الملف: " + cCatchError])
            self.oWebView.oWebView.wreturn(nId, WEBVIEW_ERROR_UNSPECIFIED, cError)
        done
        
    /*
    =========================================================================
    دالة معالجة حذف الملف
    Function: handleFileDelete
    Description: Handle file delete operation
    Input: nId (Number), cRequest (String)
    =========================================================================
    */
    func handleFileDelete nId, cRequest
        try
            aParams = json2list(cRequest)
            cFilePath = aParams[1]
            
            bResult = self.aHandlers[:file].deleteFile(cFilePath)
            cResponse = list2json([bResult])
            
            self.oWebView.oWebView.wreturn(nId, WEBVIEW_ERROR_OK, cResponse)
            
        catch
            cError = list2json(["خطأ في حذف الملف: " + cCatchError])
            self.oWebView.oWebView.wreturn(nId, WEBVIEW_ERROR_UNSPECIFIED, cError)
        done
        
    /*
    =========================================================================
    دالة معالجة فحص وجود الملف
    Function: handleFileExists
    Description: Handle file existence check
    Input: nId (Number), cRequest (String)
    =========================================================================
    */
    func handleFileExists nId, cRequest
        try
            aParams = json2list(cRequest)
            cFilePath = aParams[1]
            
            bResult = self.aHandlers[:file].fileExists(cFilePath)
            cResponse = list2json([bResult])
            
            self.oWebView.oWebView.wreturn(nId, WEBVIEW_ERROR_OK, cResponse)
            
        catch
            cError = list2json(["خطأ في فحص الملف: " + cCatchError])
            self.oWebView.oWebView.wreturn(nId, WEBVIEW_ERROR_UNSPECIFIED, cError)
        done
        
    /*
    =========================================================================
    دالة الحصول على معلومات النظام
    Function: getSystemInfo
    Description: Get system information
    Output: aInfo (List) - System information
    =========================================================================
    */
    func getSystemInfo
        return [
            :name = self.cSystemName,
            :version = self.nVersion,
            :initialized = self.bInitialized,
            :handlers_count = len(self.aHandlers),
            :methods_count = len(self.aMethodRegistry)
        ]
