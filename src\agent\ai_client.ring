# AI Client for Ring WebView IDE
# Handles communication with different AI APIs

load "internetlib.ring"
load "jsonlib.ring"

class AIClient
    cApiKey = ""
    cProvider = "gemini"  # gemini, openai, claude
    cBaseUrl = ""
    
    func init(c<PERSON><PERSON><PERSON>, c<PERSON>pi<PERSON><PERSON>)
        self.cProvider = cProvider
        self.cApiKey = cApiKey
        self.setBaseUrl()
    
    func setBaseUrl()
        switch self.cProvider
        on "gemini"
            self.cBaseUrl = "https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent"
        on "openai"
            self.cBaseUrl = "https://api.openai.com/v1/chat/completions"
        on "claude"
            self.cBaseUrl = "https://api.anthropic.com/v1/messages"
        off
    
    func sendMessage(cMessage)
        try
            cResponse = ""
            switch self.cProvider
            on "gemini"
                cResponse = self.sendGeminiMessage(cMessage)
            on "openai"
                cResponse = self.sendOpenAIMessage(cMessage)
            on "claude"
                cResponse = self.sendClaudeMessage(cMessage)
            off
            return cResponse
        catch
            return "Error: " + cCatchError
        done
    
    func sendGeminiMessage(cMessage)
        aHeaders = [
            "Content-Type: application/json"
        ]
        
        aData = [
            :contents = [
                [
                    :parts = [
                        [:text = cMessage]
                    ]
                ]
            ]
        ]
        
        cJsonData = list2json(aData)
        cUrl = self.cBaseUrl + "?key=" + self.cApiKey
        
        cResponse = download(cUrl, cJsonData, aHeaders)
        aResponse = json2list(cResponse)
        
        if len(aResponse[:candidates]) > 0
            return aResponse[:candidates][1][:content][:parts][1][:text]
        else
            return "No response from Gemini API"
        ok
    
    func sendOpenAIMessage(cMessage)
        aHeaders = [
            "Content-Type: application/json",
            "Authorization: Bearer " + self.cApiKey
        ]
        
        aData = [
            :model = "gpt-3.5-turbo",
            :messages = [
                [
                    :role = "user",
                    :content = cMessage
                ]
            ]
        ]
        
        cJsonData = list2json(aData)
        cResponse = download(self.cBaseUrl, cJsonData, aHeaders)
        aResponse = json2list(cResponse)
        
        if len(aResponse[:choices]) > 0
            return aResponse[:choices][1][:message][:content]
        else
            return "No response from OpenAI API"
        ok
    
    func sendClaudeMessage(cMessage)
        aHeaders = [
            "Content-Type: application/json",
            "x-api-key: " + self.cApiKey,
            "anthropic-version: 2023-06-01"
        ]
        
        aData = [
            :model = "claude-3-sonnet-20240229",
            :max_tokens = 1024,
            :messages = [
                [
                    :role = "user",
                    :content = cMessage
                ]
            ]
        ]
        
        cJsonData = list2json(aData)
        cResponse = download(self.cBaseUrl, cJsonData, aHeaders)
        aResponse = json2list(cResponse)
        
        if len(aResponse[:content]) > 0
            return aResponse[:content][1][:text]
        else
            return "No response from Claude API"
        ok
