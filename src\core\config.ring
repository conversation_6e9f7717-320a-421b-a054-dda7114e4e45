/*
=============================================================================
إعدادات التطبيق الأساسية
Ring WebView IDE - Core Configuration
=============================================================================
*/

# Load API keys configuration
load "../../config/api_keys.ring"

# Application configuration
aAppConfig = [
    # Window settings
    :window = [
        :title = "Ring WebView IDE - بيئة تطوير Ring",
        :width = 1200,
        :height = 800,
        :resizable = true,
        :debug = true
    ],
    
    # Editor settings
    :editor = [
        :theme = "dark",
        :font_size = 14,
        :font_family = "Consolas, 'Courier New', monospace",
        :tab_size = 4,
        :word_wrap = true,
        :line_numbers = true,
        :auto_save = true,
        :auto_save_delay = 2000
    ],
    
    # Project settings
    :project = [
        :default_extension = ".ring",
        :auto_backup = true,
        :backup_interval = 300000,
        :recent_files_limit = 10
    ],
    
    # AI Agent settings
    :agent = [
        :enabled = true,
        :auto_suggestions = true,
        :context_lines = 50,
        :max_history = 100
    ]
]

# WebView configuration (global variable required by WebView library)
aWebViewConfig = [
    :debug = aAppConfig[:window][:debug],
    :window = NULL
]

# File extensions supported
aFileExtensions = [
    ".ring", ".rh", ".txt", ".md", ".json", ".html", ".css", ".js"
]

# Default project structure
aDefaultProjectStructure = [
    "src/",
    "tests/", 
    "docs/",
    "assets/",
    "README.md",
    "main.ring"
]

/*
=============================================================================
دالة للحصول على إعدادات التطبيق
Function: getAppConfig
Description: Get application configuration section
Input: cSection (String) - Configuration section name
Output: aConfig (List) - Configuration data
=============================================================================
*/
func getAppConfig cSection
    if aAppConfig[cSection] != NULL
        return aAppConfig[cSection]
    else
        raise("قسم إعدادات غير موجود: " + cSection)
    ok

/*
=============================================================================
دالة لتحديث إعدادات التطبيق
Function: updateAppConfig
Description: Update application configuration
Input: cSection (String), aNewConfig (List)
Output: None
=============================================================================
*/
func updateAppConfig cSection, aNewConfig
    if aAppConfig[cSection] != NULL
        aAppConfig[cSection] = aNewConfig
        ? "تم تحديث إعدادات: " + cSection
    else
        raise("قسم إعدادات غير موجود: " + cSection)
    ok

/*
=============================================================================
دالة للتحقق من امتداد الملف المدعوم
Function: isSupportedFile
Description: Check if file extension is supported
Input: cFileName (String) - File name
Output: bSupported (Boolean) - File support status
=============================================================================
*/
func isSupportedFile cFileName
    cExtension = lower(right(cFileName, 5))
    
    for cSupportedExt in aFileExtensions
        if right(cExtension, len(cSupportedExt)) = cSupportedExt
            return true
        ok
    next
    
    return false
