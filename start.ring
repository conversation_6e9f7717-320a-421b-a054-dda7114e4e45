# Quick Start Script for Ring WebView IDE
# Simplified launcher with error handling

func main()
    see "=== Ring WebView IDE Launcher ===" + nl
    see "Initializing IDE..." + nl
    
    try
        # Check if WebView library is available
        if not checkWebViewLibrary()
            see "Error: WebView library not found!" + nl
            see "Please install the Ring WebView library." + nl
            return
        ok
        
        # Check configuration
        if not checkConfiguration()
            see "Warning: Configuration issues detected." + nl
            see "The IDE will run with default settings." + nl
        ok
        
        see "Starting Ring WebView IDE..." + nl
        
        # Load and run main application
        load "main.ring"
        
    catch
        see "Error starting IDE: " + cCatchError + nl
        see "Please check your Ring installation and try again." + nl
    done

func checkWebViewLibrary()
    try
        load "webview.ring"
        return true
    catch
        return false
    done

func checkConfiguration()
    try
        if fexists("config/api_keys.ring")
            load "config/api_keys.ring"
            return true
        else
            see "Configuration file not found. Creating default..." + nl
            createDefaultConfig()
            return true
        ok
    catch
        return false
    done

func createDefaultConfig()
    cDefaultConfig = `# Default API Keys Configuration
# Please update with your actual API keys

GEMINI_API_KEY = "your_gemini_api_key_here"
OPENAI_API_KEY = "your_openai_api_key_here"  
CLAUDE_API_KEY = "your_claude_api_key_here"

DEFAULT_AI_PROVIDER = "gemini"

FEATURES = [
    :ai_enabled = false,  # Disabled until API keys are configured
    :file_operations = true,
    :code_execution = true,
    :debug_mode = true
]
`
    
    write("config/api_keys.ring", cDefaultConfig)
    see "Default configuration created at config/api_keys.ring" + nl
    see "Please edit this file to add your API keys." + nl

main()
