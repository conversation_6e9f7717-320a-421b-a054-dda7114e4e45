/*
=============================================================================
WebView Wrapper - غلاف مكتبة WebView
Ring WebView IDE - WebView Wrapper
=============================================================================
*/

# Load required libraries
load "webview.ring"
load "jsonlib.ring"
load "config.ring"

/*
=============================================================================
كلاس WebViewWrapper - غلاف محسن لمكتبة WebView
Class: WebViewWrapper
Description: Enhanced wrapper for WebView library with additional features
=============================================================================
*/
class WebViewWrapper
    
    # Private properties
    private
        oWebView = NULL
        bInitialized = false
        aBindings = []
        cCurrentHtml = ""
        
    # Public properties  
    cTitle = ""
    nWidth = 1200
    nHeight = 800
    bDebugMode = true
    
    /*
    =========================================================================
    دالة البناء
    Function: init
    Description: Constructor - Initialize WebView wrapper
    =========================================================================
    */
    func init
        try
            # Configure WebView
            aWebViewConfig[:debug] = self.bDebugMode
            
            # Create WebView instance
            self.oWebView = new WebView()
            self.bInitialized = true
            
            ? "تم تهيئة WebView بنجاح"
            
        catch
            ? "خطأ في تهيئة WebView: " + cCatchError
            self.bInitialized = false
        done
        
    /*
    =========================================================================
    دالة تعيين العنوان
    Function: setTitle
    Description: Set window title
    Input: cNewTitle (String) - Window title
    =========================================================================
    */
    func setTitle cNewTitle
        if not self.bInitialized
            raise("WebView غير مهيأ")
        ok
        
        self.cTitle = cNewTitle
        self.oWebView.setTitle(cNewTitle)
        
    /*
    =========================================================================
    دالة تعيين الحجم
    Function: setSize  
    Description: Set window size
    Input: nNewWidth (Number), nNewHeight (Number)
    =========================================================================
    */
    func setSize nNewWidth, nNewHeight
        if not self.bInitialized
            raise("WebView غير مهيأ")
        ok
        
        self.nWidth = nNewWidth
        self.nHeight = nNewHeight
        self.oWebView.setSize(nNewWidth, nNewHeight, WEBVIEW_HINT_NONE)
        
    /*
    =========================================================================
    دالة تحميل HTML
    Function: loadHtml
    Description: Load HTML content
    Input: cHtmlContent (String) - HTML content
    =========================================================================
    */
    func loadHtml cHtmlContent
        if not self.bInitialized
            raise("WebView غير مهيأ")
        ok
        
        self.cCurrentHtml = cHtmlContent
        self.oWebView.setHtml(cHtmlContent)
        
    /*
    =========================================================================
    دالة تحميل ملف HTML
    Function: loadHtmlFile
    Description: Load HTML from file
    Input: cFilePath (String) - HTML file path
    =========================================================================
    */
    func loadHtmlFile cFilePath
        if not fexists(cFilePath)
            raise("ملف HTML غير موجود: " + cFilePath)
        ok
        
        cHtmlContent = read(cFilePath)
        self.loadHtml(cHtmlContent)
        
    /*
    =========================================================================
    دالة ربط الدوال
    Function: bindFunction
    Description: Bind Ring function to JavaScript
    Input: cJsName (String), cRingFunc (String)
    =========================================================================
    */
    func bindFunction cJsName, cRingFunc
        if not self.bInitialized
            raise("WebView غير مهيأ")
        ok
        
        self.oWebView.bind(cJsName, cRingFunc)
        self.aBindings + [cJsName, cRingFunc]
        
    /*
    =========================================================================
    دالة تنفيذ JavaScript
    Function: executeJS
    Description: Execute JavaScript code
    Input: cJsCode (String) - JavaScript code
    =========================================================================
    */
    func executeJS cJsCode
        if not self.bInitialized
            raise("WebView غير مهيأ")
        ok
        
        self.oWebView.evalJS(cJsCode)
        
    /*
    =========================================================================
    دالة تشغيل التطبيق
    Function: run
    Description: Run the WebView application
    =========================================================================
    */
    func run
        if not self.bInitialized
            raise("WebView غير مهيأ")
        ok
        
        ? "بدء تشغيل التطبيق..."
        self.oWebView.run()
        
    /*
    =========================================================================
    دالة إغلاق التطبيق
    Function: terminate
    Description: Terminate the WebView application
    =========================================================================
    */
    func terminate
        if self.bInitialized and self.oWebView != NULL
            self.oWebView.terminate()
        ok
        
    /*
    =========================================================================
    دالة الحصول على حالة التهيئة
    Function: isInitialized
    Description: Check if WebView is initialized
    Output: bInitialized (Boolean) - Initialization status
    =========================================================================
    */
    func isInitialized
        return self.bInitialized
