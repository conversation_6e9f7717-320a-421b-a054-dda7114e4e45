# Test Runner for Ring WebView IDE
# Quick test execution script

func main()
    see "=== Ring WebView IDE Test Runner ===" + nl
    see "Starting all tests..." + nl + nl
    
    # Run basic tests
    see "Running basic system tests..." + nl
    system("ring tests/test_basic_system.ring")
    
    see nl + "Running comprehensive system tests..." + nl
    system("ring tests/test_comprehensive_system.ring")
    
    see nl + "=== All Tests Completed ===" + nl

main()
