# Ring WebView IDE - Main Application
# Advanced IDE with AI Agent Integration

load "webview.ring"
load "src/agent/ai_agent.ring"
load "src/tools/file_manager.ring"
load "config/api_keys.ring"

# Global WebView configuration
aWebViewConfig = [
    :debug = true,
    :window = NULL
]

# Global binding list for WebView functions
aBindList = [
    ["sendToAgent", :sendToAgent],
    ["saveFile", :saveFile],
    ["loadFile", :loadFile],
    ["runCode", :runCode],
    ["createProject", :createProject],
    ["getFileList", :getFileList]
]

# Global variables
oWebView = NULL
oAgent = NULL
oFileManager = NULL

func main()
    see "Starting Ring WebView IDE..." + nl
    
    # Initialize components
    initializeComponents()
    
    # Create WebView instance
    oWebView = new WebView()
    
    # Configure WebView
    oWebView.setTitle("Ring WebView IDE - بيئة تطوير Ring المتقدمة")
    oWebView.setSize(1200, 800, WEBVIEW_HINT_NONE)
    
    # Load the main HTML interface
    cHtml = loadMainInterface()
    oWebView.setHtml(cHtml)
    
    # Inject initialization JavaScript
    oWebView.injectJS(getInitJS())
    
    see "IDE started successfully!" + nl
    
    # Run the main event loop
    oWebView.run()

func initializeComponents()
    # Initialize AI Agent
    oAgent = new AIAgent()
    
    # Initialize File Manager
    oFileManager = new FileManager()
    
    see "Components initialized successfully!" + nl

func loadMainInterface()
    cHtml = `
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ring WebView IDE</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/codemirror.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #1e1e1e;
            color: #ffffff;
            height: 100vh;
            overflow: hidden;
        }
        
        .ide-container {
            display: flex;
            height: 100vh;
        }
        
        .sidebar {
            width: 250px;
            background: #252526;
            border-left: 1px solid #3e3e42;
            display: flex;
            flex-direction: column;
        }
        
        .sidebar-header {
            padding: 15px;
            background: #2d2d30;
            border-bottom: 1px solid #3e3e42;
        }
        
        .sidebar-title {
            font-size: 14px;
            font-weight: bold;
            color: #cccccc;
        }
        
        .file-explorer {
            flex: 1;
            padding: 10px;
            overflow-y: auto;
        }
        
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
        }
        
        .toolbar {
            height: 40px;
            background: #2d2d30;
            border-bottom: 1px solid #3e3e42;
            display: flex;
            align-items: center;
            padding: 0 15px;
            gap: 10px;
        }
        
        .toolbar button {
            background: #0e639c;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
        }
        
        .toolbar button:hover {
            background: #1177bb;
        }
        
        .editor-container {
            flex: 1;
            display: flex;
        }
        
        .code-editor {
            flex: 1;
            background: #1e1e1e;
        }
        
        .chat-panel {
            width: 350px;
            background: #252526;
            border-left: 1px solid #3e3e42;
            display: flex;
            flex-direction: column;
        }
        
        .chat-header {
            padding: 15px;
            background: #2d2d30;
            border-bottom: 1px solid #3e3e42;
        }
        
        .chat-messages {
            flex: 1;
            padding: 15px;
            overflow-y: auto;
        }
        
        .chat-input-container {
            padding: 15px;
            border-top: 1px solid #3e3e42;
        }
        
        .chat-input {
            width: 100%;
            background: #3c3c3c;
            border: 1px solid #5a5a5a;
            color: white;
            padding: 10px;
            border-radius: 4px;
            resize: vertical;
            min-height: 60px;
        }
        
        .send-button {
            margin-top: 10px;
            width: 100%;
            background: #0e639c;
            color: white;
            border: none;
            padding: 10px;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .send-button:hover {
            background: #1177bb;
        }
        
        .message {
            margin-bottom: 15px;
            padding: 10px;
            border-radius: 8px;
        }
        
        .user-message {
            background: #0e639c;
            margin-left: 20px;
        }
        
        .agent-message {
            background: #2d2d30;
            margin-right: 20px;
        }
        
        .status-bar {
            height: 25px;
            background: #007acc;
            color: white;
            display: flex;
            align-items: center;
            padding: 0 15px;
            font-size: 12px;
        }
        
        .CodeMirror {
            height: 100% !important;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 14px;
        }
        
        .file-item {
            padding: 5px 10px;
            cursor: pointer;
            border-radius: 3px;
            margin-bottom: 2px;
        }
        
        .file-item:hover {
            background: #2a2d2e;
        }
        
        .file-item.selected {
            background: #094771;
        }
    </style>
</head>
<body>
    <div class="ide-container">
        <div class="sidebar">
            <div class="sidebar-header">
                <div class="sidebar-title">
                    <i class="fas fa-folder"></i> مستكشف الملفات
                </div>
            </div>
            <div class="file-explorer" id="fileExplorer">
                <div class="file-item">
                    <i class="fas fa-file-code"></i> main.ring
                </div>
                <div class="file-item">
                    <i class="fas fa-folder"></i> src/
                </div>
                <div class="file-item">
                    <i class="fas fa-folder"></i> config/
                </div>
            </div>
        </div>
        
        <div class="main-content">
            <div class="toolbar">
                <button onclick="runCode()">
                    <i class="fas fa-play"></i> تشغيل
                </button>
                <button onclick="saveFile()">
                    <i class="fas fa-save"></i> حفظ
                </button>
                <button onclick="createNewFile()">
                    <i class="fas fa-file-plus"></i> ملف جديد
                </button>
                <button onclick="createProject()">
                    <i class="fas fa-folder-plus"></i> مشروع جديد
                </button>
            </div>
            
            <div class="editor-container">
                <div class="code-editor">
                    <textarea id="codeEditor"># مرحباً بك في Ring WebView IDE
# اكتب كود Ring هنا

see "Hello, Ring WebView IDE!" + nl
</textarea>
                </div>
                
                <div class="chat-panel">
                    <div class="chat-header">
                        <div class="sidebar-title">
                            <i class="fas fa-robot"></i> الوكيل الذكي
                        </div>
                    </div>
                    <div class="chat-messages" id="chatMessages">
                        <div class="message agent-message">
                            مرحباً! أنا الوكيل الذكي لبيئة تطوير Ring. كيف يمكنني مساعدتك اليوم؟
                        </div>
                    </div>
                    <div class="chat-input-container">
                        <textarea id="chatInput" class="chat-input" placeholder="اكتب رسالتك هنا..."></textarea>
                        <button class="send-button" onclick="sendMessage()">
                            <i class="fas fa-paper-plane"></i> إرسال
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="status-bar">
        <span>جاهز | Ring WebView IDE v1.0</span>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/codemirror.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/mode/clike/clike.min.js"></script>
    <script>
        let editor;
        
        function initializeEditor() {
            editor = CodeMirror.fromTextArea(document.getElementById('codeEditor'), {
                lineNumbers: true,
                mode: 'text/x-csrc',
                theme: 'default',
                indentUnit: 4,
                lineWrapping: true,
                autoCloseBrackets: true,
                matchBrackets: true
            });
        }
        
        async function sendMessage() {
            const input = document.getElementById('chatInput');
            const message = input.value.trim();
            
            if (!message) return;
            
            // Add user message to chat
            addMessage(message, 'user');
            input.value = '';
            
            try {
                // Send to Ring backend
                const response = await window.sendToAgent(message);
                addMessage(response, 'agent');
            } catch (error) {
                addMessage('خطأ في الاتصال بالوكيل الذكي', 'agent');
            }
        }
        
        function addMessage(text, type) {
            const messagesContainer = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}-message`;
            messageDiv.textContent = text;
            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }
        
        async function runCode() {
            const code = editor.getValue();
            try {
                const result = await window.runCode(code);
                addMessage('تم تشغيل الكود بنجاح: ' + result, 'agent');
            } catch (error) {
                addMessage('خطأ في تشغيل الكود: ' + error, 'agent');
            }
        }
        
        async function saveFile() {
            const code = editor.getValue();
            try {
                await window.saveFile('current_file.ring', code);
                addMessage('تم حفظ الملف بنجاح', 'agent');
            } catch (error) {
                addMessage('خطأ في حفظ الملف', 'agent');
            }
        }
        
        function createNewFile() {
            editor.setValue('# ملف Ring جديد\n\n');
            addMessage('تم إنشاء ملف جديد', 'agent');
        }
        
        async function createProject() {
            try {
                await window.createProject();
                addMessage('تم إنشاء مشروع جديد', 'agent');
            } catch (error) {
                addMessage('خطأ في إنشاء المشروع', 'agent');
            }
        }
        
        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', function() {
            initializeEditor();
        });
        
        // Handle Enter key in chat input
        document.getElementById('chatInput').addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });
    </script>
</body>
</html>
    `
    return cHtml

func getInitJS()
    return `
        console.log('Ring WebView IDE initialized');
        window.ideReady = true;
    `

# WebView binding functions
func sendToAgent(nId, cRequest)
    try
        aArgs = json2list(cRequest)
        cMessage = aArgs[1]
        
        see "Received message: " + cMessage + nl
        
        # Send to AI Agent
        cResponse = oAgent.processMessage(cMessage)
        
        oWebView.wreturn(nId, WEBVIEW_ERROR_OK, '"' + cResponse + '"')
    catch
        oWebView.wreturn(nId, WEBVIEW_ERROR_UNSPECIFIED, '"خطأ في معالجة الرسالة"')
    done

func saveFile(nId, cRequest)
    try
        aArgs = json2list(cRequest)
        cFileName = aArgs[1]
        cContent = aArgs[2]
        
        oFileManager.saveFile(cFileName, cContent)
        
        oWebView.wreturn(nId, WEBVIEW_ERROR_OK, '"تم حفظ الملف بنجاح"')
    catch
        oWebView.wreturn(nId, WEBVIEW_ERROR_UNSPECIFIED, '"خطأ في حفظ الملف"')
    done

func loadFile(nId, cRequest)
    try
        aArgs = json2list(cRequest)
        cFileName = aArgs[1]
        
        cContent = oFileManager.loadFile(cFileName)
        
        oWebView.wreturn(nId, WEBVIEW_ERROR_OK, '"' + cContent + '"')
    catch
        oWebView.wreturn(nId, WEBVIEW_ERROR_UNSPECIFIED, '"خطأ في تحميل الملف"')
    done

func runCode(nId, cRequest)
    try
        aArgs = json2list(cRequest)
        cCode = aArgs[1]
        
        # Save code to temporary file and execute
        cTempFile = "temp_code.ring"
        write(cTempFile, cCode)
        
        # Execute Ring code (simplified)
        cResult = "تم تشغيل الكود بنجاح"
        
        oWebView.wreturn(nId, WEBVIEW_ERROR_OK, '"' + cResult + '"')
    catch
        oWebView.wreturn(nId, WEBVIEW_ERROR_UNSPECIFIED, '"خطأ في تشغيل الكود"')
    done

func createProject(nId, cRequest)
    try
        oFileManager.createProject()
        oWebView.wreturn(nId, WEBVIEW_ERROR_OK, '"تم إنشاء المشروع بنجاح"')
    catch
        oWebView.wreturn(nId, WEBVIEW_ERROR_UNSPECIFIED, '"خطأ في إنشاء المشروع"')
    done

func getFileList(nId, cRequest)
    try
        aFiles = oFileManager.getFileList()
        cFileList = list2json(aFiles)
        oWebView.wreturn(nId, WEBVIEW_ERROR_OK, cFileList)
    catch
        oWebView.wreturn(nId, WEBVIEW_ERROR_UNSPECIFIED, '"خطأ في جلب قائمة الملفات"')
    done

# Start the application
main()
