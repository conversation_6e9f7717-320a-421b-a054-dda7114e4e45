# AI Agent for Ring WebView IDE
# Advanced AI assistant with multiple provider support

load "internetlib.ring"
load "jsonlib.ring"

class AIAgent
    oAIClient = NULL
    aConversationHistory = []
    cCurrentProvider = "gemini"
    
    func init()
        # Initialize with default provider
        self.initializeAIClient()
        see "AI Agent initialized with provider: " + self.cCurrentProvider + nl
    
    func initializeAIClient()
        # Load API keys from config
        try
            load "config/api_keys.ring"
            
            switch self.cCurrentProvider
            on "gemini"
                if isDefined("GEMINI_API_KEY")
                    self.oAIClient = new AIClient("gemini", GEMINI_API_KEY)
                else
                    see "Warning: Gemini API key not found" + nl
                ok
            on "openai"
                if isDefined("OPENAI_API_KEY")
                    self.oAIClient = new AIClient("openai", OPENAI_API_KEY)
                else
                    see "Warning: OpenAI API key not found" + nl
                ok
            on "claude"
                if isDefined("CLAUDE_API_KEY")
                    self.oAIClient = new AIClient("claude", CLAUDE_API_KEY)
                else
                    see "Warning: Claude API key not found" + nl
                ok
            off
        catch
            see "Error loading API configuration: " + cCatchError + nl
        done
    
    func processMessage(cMessage)
        try
            # Add user message to history
            self.addToHistory("user", cMessage)
            
            # Process the message based on content
            cResponse = self.generateResponse(cMessage)
            
            # Add AI response to history
            self.addToHistory("assistant", cResponse)
            
            return cResponse
        catch
            return "عذراً، حدث خطأ في معالجة رسالتك: " + cCatchError
        done
    
    func generateResponse(cMessage)
        # Check if this is a code-related request
        if self.isCodeRequest(cMessage)
            return self.handleCodeRequest(cMessage)
        ok
        
        # Check if this is a file operation request
        if self.isFileRequest(cMessage)
            return self.handleFileRequest(cMessage)
        ok
        
        # General AI response
        if self.oAIClient != NULL
            cSystemPrompt = self.getSystemPrompt()
            cFullMessage = cSystemPrompt + nl + cMessage
            return self.oAIClient.sendMessage(cFullMessage)
        else
            return self.getDefaultResponse(cMessage)
        ok
    
    func isCodeRequest(cMessage)
        aCodeKeywords = ["كود", "برمجة", "دالة", "كلاس", "متغير", "حلقة", "شرط", "code", "function", "class", "variable"]
        
        for cKeyword in aCodeKeywords
            if substr(lower(cMessage), lower(cKeyword)) > 0
                return true
            ok
        next
        
        return false
    
    func isFileRequest(cMessage)
        aFileKeywords = ["ملف", "حفظ", "فتح", "إنشاء", "حذف", "file", "save", "open", "create", "delete"]
        
        for cKeyword in aFileKeywords
            if substr(lower(cMessage), lower(cKeyword)) > 0
                return true
            ok
        next
        
        return false
    
    func handleCodeRequest(cMessage)
        cResponse = "سأساعدك في البرمجة. "
        
        # Check for specific Ring language requests
        if substr(lower(cMessage), "ring") > 0
            cResponse += "إليك مثال على كود Ring:" + nl + nl
            cResponse += "# مثال على دالة Ring" + nl
            cResponse += "func sayHello(cName)" + nl
            cResponse += '    see "مرحباً " + cName + nl' + nl
            cResponse += "    return true" + nl
        else
            if self.oAIClient != NULL
                cResponse = self.oAIClient.sendMessage(cMessage)
            else
                cResponse += "يمكنني مساعدتك في كتابة وتحليل الكود. ما هو نوع المساعدة التي تحتاجها؟"
            ok
        ok
        
        return cResponse
    
    func handleFileRequest(cMessage)
        cResponse = "سأساعدك في عمليات الملفات. "
        
        if substr(lower(cMessage), "حفظ") > 0 or substr(lower(cMessage), "save") > 0
            cResponse += "لحفظ ملف، استخدم زر 'حفظ' في شريط الأدوات أو اضغط Ctrl+S"
        elseif substr(lower(cMessage), "فتح") > 0 or substr(lower(cMessage), "open") > 0
            cResponse += "لفتح ملف، استخدم مستكشف الملفات على اليسار أو اضغط Ctrl+O"
        elseif substr(lower(cMessage), "إنشاء") > 0 or substr(lower(cMessage), "create") > 0
            cResponse += "لإنشاء ملف جديد، استخدم زر 'ملف جديد' في شريط الأدوات"
        else
            cResponse += "ما هي العملية التي تريد تنفيذها على الملفات؟"
        ok
        
        return cResponse
    
    func getSystemPrompt()
        return "أنت مساعد ذكي متخصص في لغة البرمجة Ring وبيئات التطوير. " +
               "تجيب باللغة العربية وتقدم مساعدة تقنية دقيقة ومفيدة. " +
               "تركز على حل المشاكل البرمجية وتقديم أمثلة عملية."
    
    func getDefaultResponse(cMessage)
        aDefaultResponses = [
            "أفهم سؤالك. كيف يمكنني مساعدتك أكثر؟",
            "هذا سؤال مثير للاهتمام. هل يمكنك توضيح أكثر؟",
            "سأحتاج لمزيد من التفاصيل لأتمكن من مساعدتك بشكل أفضل.",
            "يمكنني مساعدتك في البرمجة وإدارة الملفات. ما هو ما تحتاجه تحديداً؟"
        ]
        
        nIndex = random(len(aDefaultResponses)) + 1
        return aDefaultResponses[nIndex]
    
    func addToHistory(cRole, cMessage)
        aEntry = [
            :role = cRole,
            :content = cMessage,
            :timestamp = date() + " " + time()
        ]
        
        add(self.aConversationHistory, aEntry)
        
        # Keep only last 20 messages to manage memory
        if len(self.aConversationHistory) > 20
            del(self.aConversationHistory, 1)
        ok
    
    func getConversationHistory()
        return self.aConversationHistory
    
    func clearHistory()
        self.aConversationHistory = []
    
    func switchProvider(cNewProvider)
        if cNewProvider != self.cCurrentProvider
            self.cCurrentProvider = cNewProvider
            self.initializeAIClient()
            see "Switched to AI provider: " + cNewProvider + nl
        ok
    
    func getAvailableProviders()
        return ["gemini", "openai", "claude"]

# AI Client class for handling different AI APIs
class AIClient
    cApiKey = ""
    cProvider = ""
    cBaseUrl = ""
    
    func init(cProvider, cApiKey)
        self.cProvider = cProvider
        self.cApiKey = cApiKey
        self.setBaseUrl()
    
    func setBaseUrl()
        switch self.cProvider
        on "gemini"
            self.cBaseUrl = "https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent"
        on "openai"
            self.cBaseUrl = "https://api.openai.com/v1/chat/completions"
        on "claude"
            self.cBaseUrl = "https://api.anthropic.com/v1/messages"
        off
    
    func sendMessage(cMessage)
        try
            cResponse = ""
            switch self.cProvider
            on "gemini"
                cResponse = self.sendGeminiMessage(cMessage)
            on "openai"
                cResponse = self.sendOpenAIMessage(cMessage)
            on "claude"
                cResponse = self.sendClaudeMessage(cMessage)
            off
            return cResponse
        catch
            return "خطأ في الاتصال بـ " + self.cProvider + ": " + cCatchError
        done
    
    func sendGeminiMessage(cMessage)
        # Simplified Gemini API call
        # In a real implementation, you would use proper HTTP requests
        return "استجابة من Gemini: " + cMessage
    
    func sendOpenAIMessage(cMessage)
        # Simplified OpenAI API call
        return "استجابة من OpenAI: " + cMessage
    
    func sendClaudeMessage(cMessage)
        # Simplified Claude API call
        return "استجابة من Claude: " + cMessage
