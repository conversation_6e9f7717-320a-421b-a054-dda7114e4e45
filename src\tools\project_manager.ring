# Project Manager for Ring WebView IDE
# Advanced project management and organization

load "src/tools/file_manager.ring"

class ProjectManager
    oFileManager = NULL
    aRecentProjects = []
    cCurrentProject = ""
    aProjectTemplates = []
    
    func init()
        self.oFileManager = new FileManager()
        self.loadProjectTemplates()
        self.loadRecentProjects()
        see "Project Manager initialized" + nl
    
    func createProject(cProjectName, cProjectPath, cTemplate)
        if cTemplate = NULL
            cTemplate = "basic"
        ok
        
        try
            # Create project directory structure
            cFullPath = cProjectPath + "/" + cProjectName
            
            # Create main directories
            self.createProjectStructure(cFullPath)
            
            # Apply template
            self.applyProjectTemplate(cFullPath, cProjectName, cTemplate)
            
            # Set as current project
            self.cCurrentProject = cFullPath
            
            # Add to recent projects
            self.addToRecentProjects(cFullPath, cProjectName)
            
            # Create project configuration
            self.createProjectConfig(cFullPath, cProjectName, cTemplate)
            
            see "Project created successfully: " + cProjectName + nl
            return true
            
        catch
            see "Error creating project: " + cCatchError + nl
            return false
        done
    
    func createProjectStructure(cProjectPath)
        # Create standard Ring project structure
        aDirectories = [
            cProjectPath,
            cProjectPath + "/src",
            cProjectPath + "/src/classes",
            cProjectPath + "/src/functions",
            cProjectPath + "/tests",
            cProjectPath + "/docs",
            cProjectPath + "/config",
            cProjectPath + "/resources",
            cProjectPath + "/build",
            cProjectPath + "/examples"
        ]
        
        for cDir in aDirectories
            self.oFileManager.createDirectory(cDir)
        next
    
    func applyProjectTemplate(cProjectPath, cProjectName, cTemplate)
        aTemplate = self.getTemplate(cTemplate)
        
        if aTemplate != NULL
            # Create files from template
            for aFile in aTemplate[:files]
                cFileName = aFile[1]
                cFileContent = aFile[2]
                
                # Replace placeholders
                cFileContent = self.replacePlaceholders(cFileContent, cProjectName)
                
                # Create file
                cFullFilePath = cProjectPath + "/" + cFileName
                self.oFileManager.saveFile(cFullFilePath, cFileContent)
            next
        else
            # Create basic project files
            self.createBasicProjectFiles(cProjectPath, cProjectName)
        ok
    
    func createBasicProjectFiles(cProjectPath, cProjectName)
        # Main Ring file
        cMainContent = "# " + cProjectName + " - Main Application" + nl +
                      "# Created: " + date() + " " + time() + nl + nl +
                      "# Load required libraries" + nl +
                      "# load \"src/classes/main_class.ring\"" + nl + nl +
                      "func main()" + nl +
                      '    see "Welcome to ' + cProjectName + '!" + nl' + nl +
                      "    # Your application code here" + nl +
                      nl +
                      "# Start the application" + nl +
                      "main()"
        
        self.oFileManager.saveFile(cProjectPath + "/main.ring", cMainContent)
        
        # README file
        cReadmeContent = "# " + cProjectName + nl + nl +
                        "## Description" + nl +
                        "Add your project description here." + nl + nl +
                        "## Installation" + nl +
                        "1. Clone the repository" + nl +
                        "2. Run: `ring main.ring`" + nl + nl +
                        "## Usage" + nl +
                        "Describe how to use your application." + nl + nl +
                        "## License" + nl +
                        "Add your license information here." + nl
        
        self.oFileManager.saveFile(cProjectPath + "/README.md", cReadmeContent)
        
        # Configuration file
        cConfigContent = "# " + cProjectName + " Configuration" + nl + nl +
                        "PROJECT_CONFIG = [" + nl +
                        '    :name = "' + cProjectName + '",' + nl +
                        '    :version = "1.0.0",' + nl +
                        '    :author = "Your Name",' + nl +
                        '    :description = "Project description",' + nl +
                        '    :created = "' + date() + '"' + nl +
                        "]" + nl
        
        self.oFileManager.saveFile(cProjectPath + "/config/project.ring", cConfigContent)
        
        # Test file
        cTestContent = "# Tests for " + cProjectName + nl + nl +
                      "load \"../main.ring\"" + nl + nl +
                      "func testMain()" + nl +
                      '    see "Running tests for ' + cProjectName + '..." + nl' + nl +
                      "    # Add your tests here" + nl +
                      '    see "All tests passed!" + nl' + nl +
                      "testMain()"
        
        self.oFileManager.saveFile(cProjectPath + "/tests/test_main.ring", cTestContent)
    
    func loadProject(cProjectPath)
        try
            if isdir(cProjectPath)
                self.cCurrentProject = cProjectPath
                
                # Load project configuration
                aProjectInfo = self.loadProjectConfig(cProjectPath)
                
                # Add to recent projects
                if aProjectInfo != NULL
                    self.addToRecentProjects(cProjectPath, aProjectInfo[:name])
                ok
                
                see "Project loaded: " + cProjectPath + nl
                return true
            else
                see "Project directory not found: " + cProjectPath + nl
                return false
            ok
        catch
            see "Error loading project: " + cCatchError + nl
            return false
        done
    
    func getCurrentProject()
        if self.cCurrentProject != ""
            return self.loadProjectConfig(self.cCurrentProject)
        else
            return NULL
        ok
    
    func loadProjectConfig(cProjectPath)
        cConfigFile = cProjectPath + "/config/project.ring"
        
        if fexists(cConfigFile)
            try
                load cConfigFile
                if isDefined("PROJECT_CONFIG")
                    return PROJECT_CONFIG
                ok
            catch
                see "Error loading project config: " + cCatchError + nl
            done
        ok
        
        # Return basic info if no config file
        aPathParts = split(cProjectPath, "/")
        cProjectName = aPathParts[len(aPathParts)]
        
        return [
            :name = cProjectName,
            :path = cProjectPath,
            :version = "Unknown",
            :author = "Unknown",
            :description = "No description available"
        ]
    
    func createProjectConfig(cProjectPath, cProjectName, cTemplate)
        aConfig = [
            :name = cProjectName,
            :path = cProjectPath,
            :template = cTemplate,
            :version = "1.0.0",
            :author = "Ring Developer",
            :description = "Ring project created with WebView IDE",
            :created = date() + " " + time(),
            :last_modified = date() + " " + time(),
            :main_file = "main.ring",
            :dependencies = []
        ]
        
        cConfigContent = "# Project Configuration for " + cProjectName + nl + nl +
                        "PROJECT_CONFIG = " + self.listToRingCode(aConfig) + nl
        
        cConfigFile = cProjectPath + "/config/project.ring"
        self.oFileManager.saveFile(cConfigFile, cConfigContent)
    
    func addToRecentProjects(cProjectPath, cProjectName)
        # Check if project already exists in recent list
        nExistingIndex = 0
        for i = 1 to len(self.aRecentProjects)
            if self.aRecentProjects[i][:path] = cProjectPath
                nExistingIndex = i
                exit
            ok
        next
        
        # Remove existing entry if found
        if nExistingIndex > 0
            del(self.aRecentProjects, nExistingIndex)
        ok
        
        # Add to beginning of list
        aProjectEntry = [
            :name = cProjectName,
            :path = cProjectPath,
            :last_opened = date() + " " + time()
        ]
        
        insert(self.aRecentProjects, 1, aProjectEntry)
        
        # Keep only last 10 projects
        if len(self.aRecentProjects) > 10
            del(self.aRecentProjects, 11)
        ok
        
        # Save recent projects
        self.saveRecentProjects()
    
    func getRecentProjects()
        return self.aRecentProjects
    
    func loadRecentProjects()
        cRecentFile = "config/recent_projects.ring"
        
        if fexists(cRecentFile)
            try
                load cRecentFile
                if isDefined("RECENT_PROJECTS")
                    self.aRecentProjects = RECENT_PROJECTS
                ok
            catch
                see "Error loading recent projects: " + cCatchError + nl
            done
        ok
    
    func saveRecentProjects()
        cRecentFile = "config/recent_projects.ring"
        cContent = "# Recent Projects" + nl + nl +
                  "RECENT_PROJECTS = " + self.listToRingCode(self.aRecentProjects) + nl
        
        self.oFileManager.saveFile(cRecentFile, cContent)
    
    func loadProjectTemplates()
        # Load templates from configuration
        try
            load "config/api_keys.ring"
            if isDefined("PROJECT_TEMPLATES")
                self.aProjectTemplates = PROJECT_TEMPLATES
            ok
        catch
            # Create default templates if config not available
            self.createDefaultTemplates()
        done
    
    func createDefaultTemplates()
        self.aProjectTemplates = [
            [
                :name = "basic",
                :description = "مشروع Ring أساسي",
                :files = [
                    ["main.ring", "# {{PROJECT_NAME}} - Main Application\n\nsee \"Hello from {{PROJECT_NAME}}!\" + nl"],
                    ["README.md", "# {{PROJECT_NAME}}\n\nBasic Ring project."]
                ]
            ],
            [
                :name = "console",
                :description = "تطبيق وحدة تحكم",
                :files = [
                    ["main.ring", "# {{PROJECT_NAME}} - Console Application\n\nfunc main()\n    see \"Welcome to {{PROJECT_NAME}}!\" + nl\n    # Your console app code here\n\nmain()"],
                    ["src/app.ring", "# Application logic for {{PROJECT_NAME}}\n\nclass App\n    func run()\n        see \"App is running...\" + nl"]
                ]
            ]
        ]
    
    func getTemplate(cTemplateName)
        for aTemplate in self.aProjectTemplates
            if aTemplate[:name] = cTemplateName
                return aTemplate
            ok
        next
        return NULL
    
    func getAvailableTemplates()
        return self.aProjectTemplates
    
    func replacePlaceholders(cContent, cProjectName)
        cContent = substr(cContent, "{{PROJECT_NAME}}", cProjectName)
        cContent = substr(cContent, "{{DATE}}", date())
        cContent = substr(cContent, "{{TIME}}", time())
        return cContent
    
    func listToRingCode(aList)
        # Convert Ring list to Ring code representation
        # This is a simplified version - can be enhanced
        cCode = "["
        
        if type(aList) = "LIST"
            for i = 1 to len(aList)
                if i > 1
                    cCode += ", "
                ok
                
                if type(aList[i]) = "LIST"
                    cCode += self.listToRingCode(aList[i])
                elseif type(aList[i]) = "STRING"
                    cCode += '"' + aList[i] + '"'
                else
                    cCode += "" + aList[i]
                ok
            next
        ok
        
        cCode += "]"
        return cCode
    
    func exportProject(cProjectPath, cExportPath, cFormat)
        # Export project in different formats
        if cFormat = NULL
            cFormat = "zip"
        ok
        
        try
            switch cFormat
            on "zip"
                return self.exportAsZip(cProjectPath, cExportPath)
            on "folder"
                return self.exportAsFolder(cProjectPath, cExportPath)
            other
                see "Unsupported export format: " + cFormat + nl
                return false
            off
        catch
            see "Error exporting project: " + cCatchError + nl
            return false
        done
    
    func exportAsFolder(cProjectPath, cExportPath)
        # Copy project to export location
        cCommand = 'Copy-Item -Path "' + cProjectPath + '" -Destination "' + cExportPath + '" -Recurse -Force'
        system(cCommand)
        return true
    
    func exportAsZip(cProjectPath, cExportPath)
        # Create ZIP archive using PowerShell
        cCommand = 'Compress-Archive -Path "' + cProjectPath + '\*" -DestinationPath "' + cExportPath + '.zip" -Force'
        system(cCommand)
        return true
    
    func getProjectStatistics(cProjectPath)
        if cProjectPath = NULL
            cProjectPath = self.cCurrentProject
        ok
        
        if cProjectPath = ""
            return NULL
        ok
        
        aStats = [
            :total_files = 0,
            :ring_files = 0,
            :total_lines = 0,
            :total_size = 0,
            :last_modified = ""
        ]
        
        # Get file list recursively
        aFiles = self.getProjectFiles(cProjectPath)
        
        for aFile in aFiles
            aStats[:total_files]++
            
            if self.oFileManager.isRingFile(aFile[:name])
                aStats[:ring_files]++
                
                # Count lines in Ring files
                cContent = read(aFile[:path])
                aLines = split(cContent, nl)
                aStats[:total_lines] += len(aLines)
            ok
            
            aStats[:total_size] += aFile[:size]
        next
        
        return aStats
    
    func getProjectFiles(cProjectPath)
        # Get all files in project recursively
        aAllFiles = []
        self.getFilesRecursive(cProjectPath, aAllFiles)
        return aAllFiles
    
    func getFilesRecursive(cDirectory, aFileList)
        aFiles = self.oFileManager.getFileList(cDirectory)
        
        for aFile in aFiles
            if aFile[:type] = "file"
                add(aFileList, aFile)
            elseif aFile[:type] = "directory" and aFile[:name] != "." and aFile[:name] != ".."
                self.getFilesRecursive(aFile[:path], aFileList)
            ok
        next
