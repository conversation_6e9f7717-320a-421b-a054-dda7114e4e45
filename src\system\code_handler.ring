/*
=============================================================================
معالج الكود الشامل
Ring WebView IDE - Comprehensive Code Handler
=============================================================================
*/

/*
=============================================================================
كلاس ComprehensiveCodeHandler - معالج الكود الشامل
Class: ComprehensiveCodeHandler
Description: Comprehensive code operations handler
=============================================================================
*/
class ComprehensiveCodeHandler
    
    # Private properties
    private
        oWebView = NULL
        aExecutionHistory = []
        cTempDir = ""
        
    # Public properties
    nMaxHistorySize = 50
    bSafeMode = true
    
    /*
    =========================================================================
    دالة البناء
    Function: init
    Description: Initialize code handler
    Input: oWebViewInstance (Object) - WebView instance
    =========================================================================
    */
    func init oWebViewInstance
        self.oWebView = oWebViewInstance
        self.cTempDir = currentdir() + "/temp/"
        
        # Create temp directory if not exists
        if not isdir(self.cTempDir)
            system("mkdir " + self.cTempDir)
        ok
        
        ? "تم تهيئة معالج الكود"
        
    /*
    =========================================================================
    دالة تنفيذ الكود
    Function: executeCode
    Description: Execute Ring code
    Input: cCode (String) - Ring code to execute
    Output: aResult (List) - Execution result [success, output, error]
    =========================================================================
    */
    func executeCode cCode
        if cCode = ""
            return [false, "", "لا يوجد كود للتنفيذ"]
        ok
        
        # Validate code in safe mode
        if self.bSafeMode
            if not self.validateCode(cCode)
                return [false, "", "الكود يحتوي على أوامر غير آمنة"]
            ok
        ok
        
        try
            # Create temporary file
            cTempFile = self.cTempDir + "temp_" + clock() + ".ring"
            write(cTempFile, cCode)
            
            # Execute code and capture output
            cCommand = "ring " + cTempFile + " 2>&1"
            cOutput = system(cCommand)
            
            # Clean up
            if fexists(cTempFile)
                remove(cTempFile)
            ok
            
            # Add to history
            self.addToHistory(cCode, cOutput)
            
            return [true, cOutput, ""]
            
        catch
            return [false, "", "خطأ في تنفيذ الكود: " + cCatchError]
        done
        
    /*
    =========================================================================
    دالة التحقق من صحة الكود
    Function: validateCode
    Description: Validate Ring code for safety
    Input: cCode (String) - Code to validate
    Output: bValid (Boolean) - Validation result
    =========================================================================
    */
    func validateCode cCode
        # List of potentially dangerous functions/commands
        aDangerousFunctions = [
            "system(", "popen(", "remove(", "rename(",
            "chdir(", "mkdir(", "rmdir(", "exec(",
            "eval(", "load \"", "import"
        ]
        
        cLowerCode = lower(cCode)
        
        for cDangerous in aDangerousFunctions
            if substr(cLowerCode, lower(cDangerous)) > 0
                return false
            ok
        next
        
        return true
        
    /*
    =========================================================================
    دالة تنسيق الكود
    Function: formatCode
    Description: Format Ring code
    Input: cCode (String) - Code to format
    Output: cFormattedCode (String) - Formatted code
    =========================================================================
    */
    func formatCode cCode
        if cCode = ""
            return ""
        ok
        
        # Basic code formatting
        aLines = str2list(cCode)
        aFormattedLines = []
        nIndentLevel = 0
        
        for cLine in aLines
            cTrimmedLine = trim(cLine)
            
            # Skip empty lines
            if cTrimmedLine = ""
                aFormattedLines + ""
                loop
            ok
            
            # Decrease indent for closing keywords
            if left(cTrimmedLine, 2) = "ok" or 
               left(cTrimmedLine, 4) = "next" or
               left(cTrimmedLine, 4) = "else" or
               left(cTrimmedLine, 6) = "elseif" or
               left(cTrimmedLine, 3) = "off" or
               left(cTrimmedLine, 5) = "other" or
               left(cTrimmedLine, 5) = "catch" or
               left(cTrimmedLine, 4) = "done"
                nIndentLevel--
                if nIndentLevel < 0
                    nIndentLevel = 0
                ok
            ok
            
            # Add indentation
            cIndent = copy("    ", nIndentLevel)
            aFormattedLines + cIndent + cTrimmedLine
            
            # Increase indent for opening keywords
            if right(cTrimmedLine, 1) = "{" or
               substr(cTrimmedLine, "if ") = 1 or
               substr(cTrimmedLine, "for ") = 1 or
               substr(cTrimmedLine, "while ") = 1 or
               substr(cTrimmedLine, "switch ") = 1 or
               substr(cTrimmedLine, "func ") = 1 or
               substr(cTrimmedLine, "class ") = 1 or
               substr(cTrimmedLine, "try") = 1 or
               left(cTrimmedLine, 2) = "on" or
               left(cTrimmedLine, 4) = "else"
                nIndentLevel++
            ok
        next
        
        return list2str(aFormattedLines)
        
    /*
    =========================================================================
    دالة إضافة للتاريخ
    Function: addToHistory
    Description: Add execution to history
    Input: cCode (String), cOutput (String)
    =========================================================================
    */
    func addToHistory cCode, cOutput
        aHistoryItem = [
            :timestamp = date() + " " + time(),
            :code = cCode,
            :output = cOutput
        ]
        
        self.aExecutionHistory + aHistoryItem
        
        # Limit history size
        if len(self.aExecutionHistory) > self.nMaxHistorySize
            del(self.aExecutionHistory, 1)
        ok
        
    /*
    =========================================================================
    دالة الحصول على التاريخ
    Function: getHistory
    Description: Get execution history
    Output: aHistory (List) - Execution history
    =========================================================================
    */
    func getHistory
        return self.aExecutionHistory
        
    /*
    =========================================================================
    دالة مسح التاريخ
    Function: clearHistory
    Description: Clear execution history
    =========================================================================
    */
    func clearHistory
        self.aExecutionHistory = []
        
    /*
    =========================================================================
    دالة تحليل الكود
    Function: analyzeCode
    Description: Analyze Ring code structure
    Input: cCode (String) - Code to analyze
    Output: aAnalysis (List) - Code analysis
    =========================================================================
    */
    func analyzeCode cCode
        aAnalysis = [
            :lines = 0,
            :functions = 0,
            :classes = 0,
            :comments = 0,
            :variables = 0
        ]
        
        if cCode = ""
            return aAnalysis
        ok
        
        aLines = str2list(cCode)
        aAnalysis[:lines] = len(aLines)
        
        for cLine in aLines
            cTrimmedLine = trim(cLine)
            
            # Count comments
            if left(cTrimmedLine, 1) = "#" or left(cTrimmedLine, 2) = "//"
                aAnalysis[:comments]++
            ok
            
            # Count functions
            if substr(cTrimmedLine, "func ") = 1
                aAnalysis[:functions]++
            ok
            
            # Count classes
            if substr(cTrimmedLine, "class ") = 1
                aAnalysis[:classes]++
            ok
            
            # Count variable assignments (basic detection)
            if substr(cTrimmedLine, " = ") > 0 and 
               not (substr(cTrimmedLine, "if ") = 1 or 
                    substr(cTrimmedLine, "while ") = 1 or
                    substr(cTrimmedLine, "for ") = 1)
                aAnalysis[:variables]++
            ok
        next
        
        return aAnalysis
