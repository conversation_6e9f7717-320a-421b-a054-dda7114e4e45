# دليل البدء السريع - Ring WebView IDE

## 🚀 البدء السريع

### 1. التشغيل الفوري
```bash
ring start.ring
```

### 2. تشغيل الاختبارات
```bash
ring run_tests.ring
```

### 3. تشغيل التطبيق الكامل
```bash
ring main.ring
```

## ⚙️ الإعداد الأولي

### 1. تكوين مفاتيح API
قم بتحرير الملف `config/api_keys.ring`:

```ring
# ضع مفاتيح API الخاصة بك هنا
GEMINI_API_KEY = "your_actual_gemini_key"
OPENAI_API_KEY = "your_actual_openai_key"
CLAUDE_API_KEY = "your_actual_claude_key"
```

### 2. الحصول على مفاتيح API

#### Gemini AI
1. اذهب إلى [Google AI Studio](https://makersuite.google.com/app/apikey)
2. قم بإنشاء مفتاح API جديد
3. انسخ المفتاح إلى الإعدادات

#### OpenAI
1. اذهب إلى [OpenAI Platform](https://platform.openai.com/api-keys)
2. قم بإنشاء مفتاح API جديد
3. انسخ المفتاح إلى الإعدادات

#### Claude (Anthropic)
1. اذهب إلى [Anthropic Console](https://console.anthropic.com/)
2. قم بإنشاء مفتاح API جديد
3. انسخ المفتاح إلى الإعدادات

## 🎯 الاستخدام الأساسي

### واجهة المستخدم

#### 1. مستكشف الملفات (يسار)
- عرض وإدارة ملفات المشروع
- إنشاء ملفات ومجلدات جديدة
- فتح المشاريع الموجودة

#### 2. محرر الكود (وسط)
- كتابة وتحرير كود Ring
- تمييز الصيغة
- أرقام الأسطر

#### 3. الوكيل الذكي (يمين)
- دردشة مع المساعد الذكي
- طلب المساعدة في البرمجة
- توليد الكود

#### 4. شريط الأدوات (أعلى)
- تشغيل الكود (F5)
- حفظ الملف (Ctrl+S)
- إنشاء ملف جديد
- إنشاء مشروع جديد

### الاختصارات المفيدة
- `F5`: تشغيل الكود
- `Ctrl+S`: حفظ الملف
- `Ctrl+N`: ملف جديد
- `Ctrl+O`: فتح ملف
- `Ctrl+F`: بحث في الكود

## 🤖 استخدام الوكيل الذكي

### أمثلة على الأسئلة:

#### للمساعدة في البرمجة:
- "كيف أنشئ دالة في Ring؟"
- "اكتب لي كود لطباعة الأرقام من 1 إلى 10"
- "ما هو الخطأ في هذا الكود؟"

#### لإدارة الملفات:
- "كيف أحفظ ملف؟"
- "كيف أنشئ مشروع جديد؟"
- "كيف أفتح ملف موجود؟"

#### للتعلم:
- "اشرح لي مفهوم الكلاسات في Ring"
- "ما هي أفضل الممارسات في Ring؟"
- "أعطني أمثلة على الحلقات"

## 📁 إنشاء مشروع جديد

### 1. من الواجهة
1. اضغط على "مشروع جديد" في شريط الأدوات
2. اختر اسم المشروع
3. اختر المكان
4. اختر القالب (اختياري)

### 2. من الكود
```ring
load "src/tools/project_manager.ring"

oProjectManager = new ProjectManager()
oProjectManager.createProject("MyProject", "C:/Projects", "basic")
```

## 🧪 تشغيل الاختبارات

### اختبارات أساسية
```bash
ring tests/test_basic_system.ring
```

### اختبارات شاملة
```bash
ring tests/test_comprehensive_system.ring
```

### جميع الاختبارات
```bash
ring run_tests.ring
```

## 🔧 استكشاف الأخطاء

### مشاكل شائعة:

#### 1. WebView لا يعمل
- تأكد من تثبيت مكتبة WebView للـ Ring
- تحقق من إصدار Ring

#### 2. الوكيل الذكي لا يستجيب
- تحقق من مفاتيح API
- تأكد من الاتصال بالإنترنت
- تحقق من حدود الاستخدام للـ API

#### 3. مشاكل في الملفات
- تحقق من صلاحيات الكتابة
- تأكد من وجود مساحة كافية
- تحقق من مسارات الملفات

### تفعيل وضع التشخيص
في `config/api_keys.ring`:
```ring
FEATURES[:debug_mode] = true
```

## 📚 موارد إضافية

### وثائق Ring
- [موقع Ring الرسمي](https://ring-lang.github.io/)
- [دليل Ring](https://ring-lang.github.io/doc/)

### مجتمع Ring
- [منتدى Ring](https://groups.google.com/forum/#!forum/ring-lang)
- [GitHub Ring](https://github.com/ring-lang/ring)

## 🆘 الحصول على المساعدة

1. **استخدم الوكيل الذكي** في التطبيق
2. **راجع الوثائق** في مجلد `docs/`
3. **شغل الاختبارات** للتأكد من سلامة النظام
4. **تحقق من ملفات السجل** في وضع التشخيص

---

**نصيحة**: ابدأ بمشروع بسيط واستخدم الوكيل الذكي للتعلم والمساعدة!
