# Code Runner for Ring WebView IDE
# Handles code execution and output capture

class CodeRunner
    cTempDir = "temp_execution"
    cCurrentFile = ""
    aExecutionHistory = []
    
    func init()
        self.createTempDirectory()
        see "Code Runner initialized" + nl
    
    func createTempDirectory()
        if not isdir(self.cTempDir)
            system('New-Item -ItemType Directory -Path "' + self.cTempDir + '" -Force')
        ok
    
    func executeCode(cCode, cFileName)
        if cFileName = NULL
            cFileName = "temp_code_" + self.generateTimestamp() + ".ring"
        ok
        
        try
            # Save code to temporary file
            cFullPath = self.cTempDir + "/" + cFileName
            write(cFullPath, cCode)
            self.cCurrentFile = cFullPath
            
            # Execute the code
            aResult = self.runRingCode(cFullPath)
            
            # Add to execution history
            self.addToHistory(cCode, cFileName, aResult)
            
            return aResult
            
        catch
            aErrorResult = [
                :success = false,
                :output = "",
                :error = "خطأ في تنفيذ الكود: " + cCatchError,
                :execution_time = 0,
                :file = cFileName
            ]
            
            self.addToHistory(cCode, cFileName, aErrorResult)
            return aErrorResult
        done
    
    func runRingCode(cFilePath)
        nStartTime = clock()
        
        try
            # Create PowerShell command to run Ring code and capture output
            cCommand = 'ring "' + cFilePath + '" 2>&1'
            
            # Execute and capture output
            cOutput = system(cCommand)
            
            nEndTime = clock()
            nExecutionTime = nEndTime - nStartTime
            
            # Check if execution was successful
            bSuccess = true
            cError = ""
            
            # Simple error detection (can be enhanced)
            if substr(lower(cOutput), "error") > 0 or substr(lower(cOutput), "exception") > 0
                bSuccess = false
                cError = cOutput
                cOutput = ""
            ok
            
            aResult = [
                :success = bSuccess,
                :output = cOutput,
                :error = cError,
                :execution_time = nExecutionTime,
                :file = cFilePath
            ]
            
            return aResult
            
        catch
            nEndTime = clock()
            nExecutionTime = nEndTime - nStartTime
            
            aResult = [
                :success = false,
                :output = "",
                :error = "فشل في تنفيذ الكود: " + cCatchError,
                :execution_time = nExecutionTime,
                :file = cFilePath
            ]
            
            return aResult
        done
    
    func validateCode(cCode)
        # Basic Ring code validation
        aValidationResult = [
            :valid = true,
            :errors = [],
            :warnings = []
        ]
        
        try
            # Check for basic syntax issues
            if self.checkBasicSyntax(cCode)
                # Check for common mistakes
                self.checkCommonMistakes(cCode, aValidationResult)
            else
                aValidationResult[:valid] = false
                add(aValidationResult[:errors], "خطأ في الصيغة الأساسية")
            ok
            
        catch
            aValidationResult[:valid] = false
            add(aValidationResult[:errors], "خطأ في التحقق من الكود: " + cCatchError)
        done
        
        return aValidationResult
    
    func checkBasicSyntax(cCode)
        # Basic syntax checks for Ring
        aLines = split(cCode, nl)
        nOpenBraces = 0
        nCloseBraces = 0
        
        for cLine in aLines
            # Count braces
            for i = 1 to len(cLine)
                if cLine[i] = "{"
                    nOpenBraces++
                elseif cLine[i] = "}"
                    nCloseBraces++
                ok
            next
        next
        
        # Check if braces are balanced
        return nOpenBraces = nCloseBraces
    
    func checkCommonMistakes(cCode, aValidationResult)
        # Check for common Ring programming mistakes
        
        # Check for missing 'ok' after 'if' statements
        if substr(cCode, "if ") > 0 and substr(cCode, "ok") = 0
            add(aValidationResult[:warnings], "تأكد من إغلاق جمل if بـ ok")
        ok
        
        # Check for missing 'next' after 'for' loops
        if substr(cCode, "for ") > 0 and substr(cCode, "next") = 0
            add(aValidationResult[:warnings], "تأكد من إغلاق حلقات for بـ next")
        ok
        
        # Check for missing 'end' after 'while' loops
        if substr(cCode, "while ") > 0 and substr(cCode, "end") = 0
            add(aValidationResult[:warnings], "تأكد من إغلاق حلقات while بـ end")
        ok
        
        # Check for undefined variables (basic check)
        if substr(cCode, "see ") > 0
            # This is a very basic check - can be enhanced
        ok
    
    func formatCode(cCode)
        # Basic code formatting for Ring
        aLines = split(cCode, nl)
        aFormattedLines = []
        nIndentLevel = 0
        
        for cLine in aLines
            cTrimmedLine = trim(cLine)
            
            if cTrimmedLine != ""
                # Decrease indent for closing keywords
                if self.isClosingKeyword(cTrimmedLine)
                    nIndentLevel--
                    if nIndentLevel < 0
                        nIndentLevel = 0
                    ok
                ok
                
                # Add indentation
                cIndent = self.getIndentation(nIndentLevel)
                cFormattedLine = cIndent + cTrimmedLine
                add(aFormattedLines, cFormattedLine)
                
                # Increase indent for opening keywords
                if self.isOpeningKeyword(cTrimmedLine)
                    nIndentLevel++
                ok
            else
                add(aFormattedLines, "")
            ok
        next
        
        # Join lines back together
        cFormattedCode = ""
        for i = 1 to len(aFormattedLines)
            cFormattedCode += aFormattedLines[i]
            if i < len(aFormattedLines)
                cFormattedCode += nl
            ok
        next
        
        return cFormattedCode
    
    func isOpeningKeyword(cLine)
        aOpeningKeywords = ["if ", "for ", "while ", "func ", "class ", "try", "{"]
        
        for cKeyword in aOpeningKeywords
            if substr(lower(cLine), lower(cKeyword)) > 0
                return true
            ok
        next
        
        return false
    
    func isClosingKeyword(cLine)
        aClosingKeywords = ["ok", "next", "end", "}", "catch", "done"]
        
        for cKeyword in aClosingKeywords
            if lower(trim(cLine)) = lower(cKeyword)
                return true
            ok
        next
        
        return false
    
    func getIndentation(nLevel)
        cIndent = ""
        for i = 1 to nLevel
            cIndent += "    "  # 4 spaces per level
        next
        return cIndent
    
    func addToHistory(cCode, cFileName, aResult)
        aHistoryEntry = [
            :timestamp = date() + " " + time(),
            :code = cCode,
            :filename = cFileName,
            :result = aResult
        ]
        
        add(self.aExecutionHistory, aHistoryEntry)
        
        # Keep only last 50 executions
        if len(self.aExecutionHistory) > 50
            del(self.aExecutionHistory, 1)
        ok
    
    func getExecutionHistory()
        return self.aExecutionHistory
    
    func clearHistory()
        self.aExecutionHistory = []
    
    func generateTimestamp()
        cTimestamp = date() + "_" + time()
        cTimestamp = substr(cTimestamp, " ", "_")
        cTimestamp = substr(cTimestamp, ":", "_")
        cTimestamp = substr(cTimestamp, "/", "_")
        return cTimestamp
    
    func getLastExecution()
        if len(self.aExecutionHistory) > 0
            return self.aExecutionHistory[len(self.aExecutionHistory)]
        else
            return NULL
        ok
    
    func saveExecutionResult(aResult, cOutputFile)
        try
            cContent = "=== Ring Code Execution Result ===" + nl
            cContent += "Timestamp: " + date() + " " + time() + nl
            cContent += "File: " + aResult[:file] + nl
            cContent += "Success: " + aResult[:success] + nl
            cContent += "Execution Time: " + aResult[:execution_time] + "ms" + nl + nl
            
            if aResult[:success]
                cContent += "Output:" + nl + aResult[:output] + nl
            else
                cContent += "Error:" + nl + aResult[:error] + nl
            ok
            
            write(cOutputFile, cContent)
            return true
            
        catch
            return false
        done
    
    func cleanup()
        # Clean up temporary files
        try
            if isdir(self.cTempDir)
                system('Remove-Item -Path "' + self.cTempDir + '" -Recurse -Force')
            ok
            see "Code Runner cleanup completed" + nl
        catch
            see "Error during cleanup: " + cCatchError + nl
        done
    
    func getCodeStatistics(cCode)
        aStats = [
            :lines = 0,
            :characters = 0,
            :words = 0,
            :functions = 0,
            :classes = 0,
            :comments = 0
        ]
        
        aLines = split(cCode, nl)
        aStats[:lines] = len(aLines)
        aStats[:characters] = len(cCode)
        
        # Count words, functions, classes, and comments
        for cLine in aLines
            cTrimmedLine = trim(cLine)
            
            # Count words
            if cTrimmedLine != ""
                aWords = split(cTrimmedLine, " ")
                aStats[:words] += len(aWords)
            ok
            
            # Count functions
            if substr(lower(cTrimmedLine), "func ") > 0
                aStats[:functions]++
            ok
            
            # Count classes
            if substr(lower(cTrimmedLine), "class ") > 0
                aStats[:classes]++
            ok
            
            # Count comments
            if substr(cTrimmedLine, "#") > 0
                aStats[:comments]++
            ok
        next
        
        return aStats
