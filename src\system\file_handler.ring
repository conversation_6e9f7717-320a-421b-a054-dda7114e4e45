/*
=============================================================================
معالج الملفات الشامل
Ring WebView IDE - Comprehensive File Handler
=============================================================================
*/

/*
=============================================================================
كلاس ComprehensiveFileHandler - معالج الملفات الشامل
Class: ComprehensiveFileHandler
Description: Comprehensive file operations handler
=============================================================================
*/
class ComprehensiveFileHandler
    
    # Private properties
    private
        oWebView = NULL
        aRecentFiles = []
        cCurrentDirectory = ""
        
    # Public properties
    nMaxRecentFiles = 10
    bAutoBackup = true
    
    /*
    =========================================================================
    دالة البناء
    Function: init
    Description: Initialize file handler
    Input: oWebViewInstance (Object) - WebView instance
    =========================================================================
    */
    func init oWebViewInstance
        self.oWebView = oWebViewInstance
        self.cCurrentDirectory = currentdir()
        
        ? "تم تهيئة معالج الملفات"
        
    /*
    =========================================================================
    دالة قراءة الملف
    Function: readFile
    Description: Read file content
    Input: cFilePath (String) - File path
    Output: cContent (String) - File content
    =========================================================================
    */
    func readFile cFilePath
        if not self.fileExists(cFilePath)
            raise("الملف غير موجود: " + cFilePath)
        ok
        
        try
            cContent = read(cFilePath)
            self.addToRecentFiles(cFilePath)
            return cContent
            
        catch
            raise("خطأ في قراءة الملف: " + cCatchError)
        done
        
    /*
    =========================================================================
    دالة كتابة الملف
    Function: writeFile
    Description: Write content to file
    Input: cFilePath (String), cContent (String)
    Output: bSuccess (Boolean) - Write success status
    =========================================================================
    */
    func writeFile cFilePath, cContent
        try
            # Create backup if enabled
            if self.bAutoBackup and self.fileExists(cFilePath)
                self.createBackup(cFilePath)
            ok
            
            # Write file
            write(cFilePath, cContent)
            self.addToRecentFiles(cFilePath)
            
            return true
            
        catch
            raise("خطأ في كتابة الملف: " + cCatchError)
        done
        
    /*
    =========================================================================
    دالة حذف الملف
    Function: deleteFile
    Description: Delete file
    Input: cFilePath (String) - File path
    Output: bSuccess (Boolean) - Delete success status
    =========================================================================
    */
    func deleteFile cFilePath
        if not self.fileExists(cFilePath)
            raise("الملف غير موجود: " + cFilePath)
        ok
        
        try
            # Create backup before deletion
            if self.bAutoBackup
                self.createBackup(cFilePath)
            ok
            
            remove(cFilePath)
            self.removeFromRecentFiles(cFilePath)
            
            return true
            
        catch
            raise("خطأ في حذف الملف: " + cCatchError)
        done
        
    /*
    =========================================================================
    دالة فحص وجود الملف
    Function: fileExists
    Description: Check if file exists
    Input: cFilePath (String) - File path
    Output: bExists (Boolean) - File existence status
    =========================================================================
    */
    func fileExists cFilePath
        return fexists(cFilePath)
        
    /*
    =========================================================================
    دالة إنشاء نسخة احتياطية
    Function: createBackup
    Description: Create file backup
    Input: cFilePath (String) - Original file path
    =========================================================================
    */
    func createBackup cFilePath
        if not self.fileExists(cFilePath)
            return
        ok
        
        cBackupPath = cFilePath + ".backup." + date() + "." + time()
        cBackupPath = substr(cBackupPath, " ", "_")
        cBackupPath = substr(cBackupPath, ":", "-")
        
        try
            cContent = read(cFilePath)
            write(cBackupPath, cContent)
            
        catch
            ? "تحذير: فشل في إنشاء نسخة احتياطية: " + cCatchError
        done
        
    /*
    =========================================================================
    دالة إضافة للملفات الحديثة
    Function: addToRecentFiles
    Description: Add file to recent files list
    Input: cFilePath (String) - File path
    =========================================================================
    */
    func addToRecentFiles cFilePath
        # Remove if already exists
        self.removeFromRecentFiles(cFilePath)
        
        # Add to beginning
        self.aRecentFiles = [cFilePath] + self.aRecentFiles
        
        # Limit size
        if len(self.aRecentFiles) > self.nMaxRecentFiles
            self.aRecentFiles = left(self.aRecentFiles, self.nMaxRecentFiles)
        ok
        
    /*
    =========================================================================
    دالة إزالة من الملفات الحديثة
    Function: removeFromRecentFiles
    Description: Remove file from recent files list
    Input: cFilePath (String) - File path
    =========================================================================
    */
    func removeFromRecentFiles cFilePath
        nIndex = find(self.aRecentFiles, cFilePath)
        if nIndex > 0
            del(self.aRecentFiles, nIndex)
        ok
        
    /*
    =========================================================================
    دالة الحصول على الملفات الحديثة
    Function: getRecentFiles
    Description: Get recent files list
    Output: aFiles (List) - Recent files list
    =========================================================================
    */
    func getRecentFiles
        return self.aRecentFiles
        
    /*
    =========================================================================
    دالة الحصول على قائمة الملفات في مجلد
    Function: listFiles
    Description: List files in directory
    Input: cDirPath (String) - Directory path
    Output: aFiles (List) - Files list
    =========================================================================
    */
    func listFiles cDirPath
        if cDirPath = ""
            cDirPath = self.cCurrentDirectory
        ok
        
        try
            aFiles = []
            aAllFiles = dir(cDirPath)
            
            for aFileInfo in aAllFiles
                cFileName = aFileInfo[1]
                if aFileInfo[2] = false  # Not a directory
                    aFiles + cFileName
                ok
            next
            
            return aFiles
            
        catch
            raise("خطأ في قراءة المجلد: " + cCatchError)
        done
        
    /*
    =========================================================================
    دالة تغيير المجلد الحالي
    Function: changeDirectory
    Description: Change current directory
    Input: cNewDir (String) - New directory path
    =========================================================================
    */
    func changeDirectory cNewDir
        if not isdir(cNewDir)
            raise("المجلد غير موجود: " + cNewDir)
        ok
        
        self.cCurrentDirectory = cNewDir
        chdir(cNewDir)
