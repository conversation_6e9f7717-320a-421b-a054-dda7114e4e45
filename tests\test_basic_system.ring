# Basic System Tests for Ring WebView IDE
# Tests core functionality and components

load "../src/agent/ai_agent.ring"
load "../src/tools/file_manager.ring"

# Test Results
aTestResults = []
nPassedTests = 0
nFailedTests = 0

func main()
    see "=== Ring WebView IDE - Basic System Tests ===" + nl + nl
    
    # Run all tests
    testFileManager()
    testAIAgent()
    testConfiguration()
    
    # Display results
    displayTestResults()

func testFileManager()
    see "Testing File Manager..." + nl
    
    # Test 1: Initialize File Manager
    runTest("FileManager Initialization", :testFileManagerInit)
    
    # Test 2: Create and Save File
    runTest("File Creation and Saving", :testFileSave)
    
    # Test 3: Load File
    runTest("File Loading", :testFileLoad)
    
    # Test 4: Create Directory
    runTest("Directory Creation", :testDirectoryCreation)
    
    # Test 5: Project Creation
    runTest("Project Creation", :testProjectCreation)
    
    see nl

func testAIAgent()
    see "Testing AI Agent..." + nl
    
    # Test 1: Initialize AI Agent
    runTest("AIAgent Initialization", :testAIAgentInit)
    
    # Test 2: Process Message
    runTest("Message Processing", :testMessageProcessing)
    
    # Test 3: Code Request Handling
    runTest("Code Request Handling", :testCodeRequestHandling)
    
    # Test 4: File Request Handling
    runTest("File Request Handling", :testFileRequestHandling)
    
    see nl

func testConfiguration()
    see "Testing Configuration..." + nl
    
    # Test 1: Load Configuration
    runTest("Configuration Loading", :testConfigLoad)
    
    # Test 2: API Keys
    runTest("API Keys Configuration", :testAPIKeys)
    
    see nl

# File Manager Tests
func testFileManagerInit()
    try
        oFileManager = new FileManager()
        return oFileManager != NULL
    catch
        return false
    done

func testFileSave()
    try
        oFileManager = new FileManager()
        cTestFile = "test_file.ring"
        cTestContent = "# Test file content" + nl + 'see "Hello, Test!" + nl'
        
        bResult = oFileManager.saveFile(cTestFile, cTestContent)
        
        # Clean up
        if fexists(cTestFile)
            remove(cTestFile)
        ok
        
        return bResult
    catch
        return false
    done

func testFileLoad()
    try
        oFileManager = new FileManager()
        cTestFile = "test_load.ring"
        cTestContent = "# Test load content"
        
        # Create test file
        write(cTestFile, cTestContent)
        
        # Load file
        cLoadedContent = oFileManager.loadFile(cTestFile)
        
        # Clean up
        if fexists(cTestFile)
            remove(cTestFile)
        ok
        
        return cLoadedContent = cTestContent
    catch
        return false
    done

func testDirectoryCreation()
    try
        oFileManager = new FileManager()
        cTestDir = "test_directory"
        
        bResult = oFileManager.createDirectory(cTestDir)
        
        # Clean up
        if isdir(cTestDir)
            system('Remove-Item -Path "' + cTestDir + '" -Recurse -Force')
        ok
        
        return bResult
    catch
        return false
    done

func testProjectCreation()
    try
        oFileManager = new FileManager()
        cProjectName = "TestProject"
        cProjectPath = "test_projects/" + cProjectName
        
        bResult = oFileManager.createProject(cProjectName, cProjectPath)
        
        # Clean up
        if isdir("test_projects")
            system('Remove-Item -Path "test_projects" -Recurse -Force')
        ok
        
        return bResult
    catch
        return false
    done

# AI Agent Tests
func testAIAgentInit()
    try
        oAgent = new AIAgent()
        return oAgent != NULL
    catch
        return false
    done

func testMessageProcessing()
    try
        oAgent = new AIAgent()
        cMessage = "مرحبا، كيف يمكنني إنشاء دالة في Ring؟"
        cResponse = oAgent.processMessage(cMessage)
        
        return len(cResponse) > 0
    catch
        return false
    done

func testCodeRequestHandling()
    try
        oAgent = new AIAgent()
        cMessage = "أريد كتابة كود Ring لطباعة الأرقام من 1 إلى 10"
        cResponse = oAgent.processMessage(cMessage)
        
        # Check if response contains code-related content
        return substr(lower(cResponse), "ring") > 0 or substr(lower(cResponse), "كود") > 0
    catch
        return false
    done

func testFileRequestHandling()
    try
        oAgent = new AIAgent()
        cMessage = "كيف يمكنني حفظ ملف؟"
        cResponse = oAgent.processMessage(cMessage)
        
        # Check if response contains file-related content
        return substr(lower(cResponse), "حفظ") > 0 or substr(lower(cResponse), "ملف") > 0
    catch
        return false
    done

# Configuration Tests
func testConfigLoad()
    try
        load "../config/api_keys.ring"
        return isDefined("GEMINI_API_KEY")
    catch
        return false
    done

func testAPIKeys()
    try
        load "../config/api_keys.ring"
        return isDefined("GEMINI_API_KEY") and 
               isDefined("OPENAI_API_KEY") and 
               isDefined("CLAUDE_API_KEY")
    catch
        return false
    done

# Test Framework Functions
func runTest(cTestName, pTestFunc)
    try
        bResult = call pTestFunc()
        
        if bResult
            see "✓ " + cTestName + " - PASSED" + nl
            nPassedTests++
            add(aTestResults, [cTestName, "PASSED", ""])
        else
            see "✗ " + cTestName + " - FAILED" + nl
            nFailedTests++
            add(aTestResults, [cTestName, "FAILED", "Test returned false"])
        ok
        
    catch
        see "✗ " + cTestName + " - ERROR: " + cCatchError + nl
        nFailedTests++
        add(aTestResults, [cTestName, "ERROR", cCatchError])
    done

func displayTestResults()
    see "=== Test Results Summary ===" + nl
    see "Total Tests: " + (nPassedTests + nFailedTests) + nl
    see "Passed: " + nPassedTests + nl
    see "Failed: " + nFailedTests + nl
    
    if nFailedTests > 0
        see nl + "Failed Tests:" + nl
        for aResult in aTestResults
            if aResult[2] != "PASSED"
                see "- " + aResult[1] + ": " + aResult[3] + nl
            ok
        next
    ok
    
    see nl + "Test Coverage:" + nl
    see "- File Manager: " + getTestCoverage("FileManager") + "%" + nl
    see "- AI Agent: " + getTestCoverage("AIAgent") + "%" + nl
    see "- Configuration: " + getTestCoverage("Configuration") + "%" + nl
    
    if nFailedTests = 0
        see nl + "🎉 All tests passed successfully!" + nl
    else
        see nl + "⚠️  Some tests failed. Please check the implementation." + nl
    ok

func getTestCoverage(cComponent)
    nTotal = 0
    nPassed = 0
    
    for aResult in aTestResults
        if substr(aResult[1], cComponent) > 0
            nTotal++
            if aResult[2] = "PASSED"
                nPassed++
            ok
        ok
    next
    
    if nTotal > 0
        return floor((nPassed / nTotal) * 100)
    else
        return 0
    ok

# Helper function to check if a variable is defined
func isDefined(cVarName)
    try
        eval("return " + cVarName + " != NULL")
        return true
    catch
        return false
    done
