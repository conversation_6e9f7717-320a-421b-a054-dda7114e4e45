# Ring WebView IDE with AI Agent

A comprehensive Integrated Development Environment (IDE) for the Ring programming language, powered by an advanced AI agent and built with WebView technology.

## 🌟 Features

- **Modern Web Interface**: Built with WebView for a responsive and modern UI
- **AI-Powered Agent**: Integrated with multiple AI APIs (Gemini, OpenAI, Claude)
- **Arabic Language Support**: Full RTL support and Arabic interface
- **Advanced Code Editor**: Syntax highlighting with CodeMirror
- **Project Management**: Complete project and file management system
- **Real-time Code Execution**: Run and test Ring code directly in the IDE
- **Intelligent Assistant**: Context-aware AI help for coding and debugging

## 🏗️ Architecture

### Core Components
- **WebView Layer**: Modern web-based user interface
- **AI Agent**: Intelligent assistant with multiple AI model support
- **Ring Backend**: Core application logic and Ring language integration
- **Tool System**: Comprehensive set of development tools

### Directory Structure
```
├── main.ring              # Main application entry point
├── src/                   # Source code
│   ├── agent/            # AI Agent components
│   │   └── ai_agent.ring # Main AI agent implementation
│   └── tools/            # Development tools
│       └── file_manager.ring # File operations manager
├── config/               # Configuration files
│   └── api_keys.ring    # API keys and settings
├── tests/               # Test files
│   ├── test_basic_system.ring
│   └── test_comprehensive_system.ring
└── docs/                # Documentation
```

## 📋 Requirements

- Ring Programming Language (latest version)
- WebView library for Ring
- Internet connection (for AI features)
- Windows PowerShell (for file operations)

## 🚀 Installation

1. **Clone or download** this repository
2. **Configure API keys** in `config/api_keys.ring`:
   ```ring
   GEMINI_API_KEY = "your_gemini_api_key_here"
   OPENAI_API_KEY = "your_openai_api_key_here"
   CLAUDE_API_KEY = "your_claude_api_key_here"
   ```
3. **Run the application**:
   ```bash
   ring main.ring
   ```

## ⚙️ Configuration

### API Keys Setup
Edit `config/api_keys.ring` to configure your AI service API keys:

- **Gemini AI**: Get your key from [Google AI Studio](https://makersuite.google.com/app/apikey)
- **OpenAI**: Get your key from [OpenAI Platform](https://platform.openai.com/api-keys)
- **Claude**: Get your key from [Anthropic Console](https://console.anthropic.com/)

### IDE Settings
The configuration file also includes:
- Theme settings (dark/light)
- Language preferences (Arabic/English)
- Editor preferences (font size, tab size, etc.)
- Feature flags for enabling/disabling components

## 🎯 Usage

### Starting the IDE
```bash
ring main.ring
```

### Main Interface Components

1. **File Explorer** (Left Panel)
   - Browse and manage project files
   - Create new files and folders
   - Open existing projects

2. **Code Editor** (Center Panel)
   - Advanced Ring code editor with syntax highlighting
   - Auto-completion and bracket matching
   - Line numbers and code folding

3. **AI Chat Panel** (Right Panel)
   - Interactive chat with AI assistant
   - Code generation and explanation
   - Debugging help and suggestions

4. **Toolbar** (Top)
   - Run code (F5)
   - Save file (Ctrl+S)
   - Create new file/project
   - Additional tools and options

### Keyboard Shortcuts
- `Ctrl+S`: Save current file
- `Ctrl+O`: Open file
- `Ctrl+N`: New file
- `F5`: Run code
- `Ctrl+R`: Run code
- `Ctrl+F`: Find in code
- `Ctrl+/`: Toggle comment

### AI Assistant Features
The AI assistant can help with:
- **Code Generation**: Write Ring functions and classes
- **Code Explanation**: Understand existing code
- **Debugging**: Find and fix errors
- **Best Practices**: Ring programming guidelines
- **Project Structure**: Organize your code effectively

## 🧪 Testing

Run the test suites to verify the system:

### Basic Tests
```bash
ring tests/test_basic_system.ring
```

### Comprehensive Tests
```bash
ring tests/test_comprehensive_system.ring
```

The test suites cover:
- File Manager operations
- AI Agent functionality
- Integration between components
- Performance benchmarks
- Error handling scenarios

## 🔧 Development

### Adding New Features
1. Create new modules in the `src/` directory
2. Add corresponding tests in `tests/`
3. Update configuration if needed
4. Document the new features

### Extending AI Capabilities
1. Add new AI providers in `src/agent/ai_agent.ring`
2. Configure API endpoints and authentication
3. Test with different AI models
4. Update the UI to support new features

## 🐛 Troubleshooting

### Common Issues

1. **WebView not loading**
   - Ensure WebView library is properly installed
   - Check if the HTML content is valid

2. **AI features not working**
   - Verify API keys are correctly configured
   - Check internet connection
   - Ensure API quotas are not exceeded

3. **File operations failing**
   - Check file permissions
   - Verify PowerShell is available
   - Ensure sufficient disk space

### Debug Mode
Enable debug mode in `config/api_keys.ring`:
```ring
FEATURES[:debug_mode] = true
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## 📄 License

This project is open source and available under the MIT License.

## 🙏 Acknowledgments

- Ring Programming Language team
- WebView library contributors
- AI service providers (Google, OpenAI, Anthropic)
- Open source community

---

**Made with ❤️ for the Ring programming community**
