# File Manager for Ring WebView IDE
# Handles all file operations and project management

class FileManager
    cCurrentProject = ""
    aOpenFiles = []
    cWorkingDirectory = ""
    
    func init()
        self.cWorkingDirectory = currentdir()
        see "File Manager initialized in: " + self.cWorkingDirectory + nl
    
    func saveFile(cFileName, cContent)
        try
            # Ensure directory exists
            cDir = self.getDirectoryFromPath(cFileName)
            if cDir != ""
                self.createDirectory(cDir)
            ok
            
            # Write file content
            write(cFileName, cContent)
            
            # Add to open files list if not already there
            if not self.isFileOpen(cFileName)
                add(self.aOpenFiles, cFileName)
            ok
            
            see "File saved: " + cFileName + nl
            return true
        catch
            see "Error saving file " + cFileName + ": " + cCatchError + nl
            return false
        done
    
    func loadFile(cFileName)
        try
            if fexists(cFileName)
                cContent = read(cFileName)
                
                # Add to open files list if not already there
                if not self.isFileOpen(cFileName)
                    add(self.aOpenFiles, cFileName)
                ok
                
                see "File loaded: " + cFileName + nl
                return cContent
            else
                raise("File not found: " + cFileName)
            ok
        catch
            see "Error loading file " + cFileName + ": " + cCatchError + nl
            return ""
        done
    
    func createFile(cFileName, cContent)
        if cContent = NULL
            cContent = "# New Ring file" + nl + nl
        ok
        
        return self.saveFile(cFileName, cContent)
    
    func deleteFile(cFileName)
        try
            if fexists(cFileName)
                remove(cFileName)
                
                # Remove from open files list
                nIndex = find(self.aOpenFiles, cFileName)
                if nIndex > 0
                    del(self.aOpenFiles, nIndex)
                ok
                
                see "File deleted: " + cFileName + nl
                return true
            else
                see "File not found: " + cFileName + nl
                return false
            ok
        catch
            see "Error deleting file " + cFileName + ": " + cCatchError + nl
            return false
        done
    
    func createDirectory(cDirPath)
        try
            # Create directory if it doesn't exist
            if not isdir(cDirPath)
                # Use PowerShell command to create directory
                cCommand = 'New-Item -ItemType Directory -Path "' + cDirPath + '" -Force'
                system(cCommand)
                see "Directory created: " + cDirPath + nl
            ok
            return true
        catch
            see "Error creating directory " + cDirPath + ": " + cCatchError + nl
            return false
        done
    
    func getFileList(cDirectory)
        if cDirectory = NULL
            cDirectory = self.cWorkingDirectory
        ok
        
        aFiles = []
        
        try
            # Get list of files in directory
            aFileList = dir(cDirectory)
            
            for aFileInfo in aFileList
                cFileName = aFileInfo[1]
                cFileType = aFileInfo[2]
                
                # Skip . and .. entries
                if cFileName != "." and cFileName != ".."
                    aFileEntry = [
                        :name = cFileName,
                        :type = cFileType,
                        :path = cDirectory + "/" + cFileName,
                        :size = aFileInfo[3],
                        :date = aFileInfo[4]
                    ]
                    add(aFiles, aFileEntry)
                ok
            next
            
        catch
            see "Error getting file list: " + cCatchError + nl
        done
        
        return aFiles
    
    func createProject(cProjectName, cProjectPath)
        if cProjectName = NULL
            cProjectName = "NewRingProject"
        ok
        
        if cProjectPath = NULL
            cProjectPath = self.cWorkingDirectory + "/" + cProjectName
        ok
        
        try
            # Create project directory structure
            self.createDirectory(cProjectPath)
            self.createDirectory(cProjectPath + "/src")
            self.createDirectory(cProjectPath + "/tests")
            self.createDirectory(cProjectPath + "/docs")
            self.createDirectory(cProjectPath + "/config")
            
            # Create main project file
            cMainFile = cProjectPath + "/main.ring"
            cMainContent = "# " + cProjectName + " - Main File" + nl +
                          "# Created: " + date() + " " + time() + nl + nl +
                          'see "Welcome to ' + cProjectName + '!" + nl'
            
            self.saveFile(cMainFile, cMainContent)
            
            # Create README file
            cReadmeFile = cProjectPath + "/README.md"
            cReadmeContent = "# " + cProjectName + nl + nl +
                           "Ring project created on " + date() + nl + nl +
                           "## Description" + nl +
                           "Add your project description here." + nl + nl +
                           "## Usage" + nl +
                           "Run: `ring main.ring`" + nl
            
            self.saveFile(cReadmeFile, cReadmeContent)
            
            # Set as current project
            self.cCurrentProject = cProjectPath
            
            see "Project created: " + cProjectName + " at " + cProjectPath + nl
            return true
            
        catch
            see "Error creating project: " + cCatchError + nl
            return false
        done
    
    func openProject(cProjectPath)
        try
            if isdir(cProjectPath)
                self.cCurrentProject = cProjectPath
                self.cWorkingDirectory = cProjectPath
                
                # Load project files
                aProjectFiles = self.getFileList(cProjectPath)
                
                see "Project opened: " + cProjectPath + nl
                see "Found " + len(aProjectFiles) + " files" + nl
                
                return true
            else
                see "Project directory not found: " + cProjectPath + nl
                return false
            ok
        catch
            see "Error opening project: " + cCatchError + nl
            return false
        done
    
    func getProjectInfo()
        if self.cCurrentProject != ""
            aInfo = [
                :name = self.getProjectName(),
                :path = self.cCurrentProject,
                :files = self.getFileList(self.cCurrentProject),
                :openFiles = self.aOpenFiles
            ]
            return aInfo
        else
            return NULL
        ok
    
    func getProjectName()
        if self.cCurrentProject != ""
            # Extract project name from path
            aPathParts = split(self.cCurrentProject, "/")
            if len(aPathParts) > 0
                return aPathParts[len(aPathParts)]
            ok
        ok
        return "Untitled Project"
    
    func isFileOpen(cFileName)
        return find(self.aOpenFiles, cFileName) > 0
    
    func closeFile(cFileName)
        nIndex = find(self.aOpenFiles, cFileName)
        if nIndex > 0
            del(self.aOpenFiles, nIndex)
            see "File closed: " + cFileName + nl
            return true
        ok
        return false
    
    func getOpenFiles()
        return self.aOpenFiles
    
    func getDirectoryFromPath(cFilePath)
        nLastSlash = 0
        for i = 1 to len(cFilePath)
            if cFilePath[i] = "/" or cFilePath[i] = "\"
                nLastSlash = i
            ok
        next
        
        if nLastSlash > 0
            return substr(cFilePath, 1, nLastSlash - 1)
        ok
        
        return ""
    
    func getFileExtension(cFileName)
        nLastDot = 0
        for i = len(cFileName) to 1 step -1
            if cFileName[i] = "."
                nLastDot = i
                exit
            ok
        next
        
        if nLastDot > 0
            return substr(cFileName, nLastDot + 1, len(cFileName))
        ok
        
        return ""
    
    func isRingFile(cFileName)
        cExt = lower(self.getFileExtension(cFileName))
        return cExt = "ring"
    
    func backupFile(cFileName)
        try
            if fexists(cFileName)
                cBackupName = cFileName + ".backup." + date() + "." + time()
                cBackupName = substr(cBackupName, " ", "_")
                cBackupName = substr(cBackupName, ":", "_")
                
                cContent = read(cFileName)
                write(cBackupName, cContent)
                
                see "Backup created: " + cBackupName + nl
                return cBackupName
            ok
        catch
            see "Error creating backup: " + cCatchError + nl
        done
        
        return ""
