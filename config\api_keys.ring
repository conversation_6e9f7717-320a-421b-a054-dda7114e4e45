/*
=============================================================================
ملف إعدادات مفاتيح API للوكيل الذكي
Ring WebView IDE - API Keys Configuration
=============================================================================
*/

# Global configuration for AI APIs
aApiKeys = [
    # OpenAI Configuration
    :openai = [
        :api_key = "",
        :model = "gpt-4",
        :base_url = "https://api.openai.com/v1",
        :enabled = false
    ],
    
    # Google Gemini Configuration  
    :gemini = [
        :api_key = "",
        :model = "gemini-pro",
        :base_url = "https://generativelanguage.googleapis.com/v1beta",
        :enabled = false
    ],
    
    # Anthropic Claude Configuration
    :claude = [
        :api_key = "",
        :model = "claude-3-sonnet-20240229",
        :base_url = "https://api.anthropic.com/v1",
        :enabled = false
    ]
]

# Default AI provider
cDefaultProvider = "gemini"

# Request timeout in seconds
nRequestTimeout = 30

# Maximum tokens for responses
nMaxTokens = 2048

/*
=============================================================================
دالة للحصول على إعدادات مزود معين
Function: getProviderConfig
Description: Get configuration for specific AI provider
Input: cProvider (String) - Provider name
Output: aConfig (List) - Provider configuration
=============================================================================
*/
func getProviderConfig cProvider
    if aApiKeys[cProvider] != NULL
        return aApiKeys[cProvider]
    else
        raise("مزود غير مدعوم: " + cProvider)
    ok

/*
=============================================================================
دالة للتحقق من صحة الإعدادات
Function: validateConfig
Description: Validate API configuration
Input: None
Output: bValid (Boolean) - Configuration validity
=============================================================================
*/
func validateConfig
    bHasValidProvider = false
    
    for cProvider in [:openai, :gemini, :claude]
        aConfig = aApiKeys[cProvider]
        if aConfig[:enabled] and len(aConfig[:api_key]) > 0
            bHasValidProvider = true
            exit
        ok
    next
    
    return bHasValidProvider
