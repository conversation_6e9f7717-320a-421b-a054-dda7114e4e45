# API Keys Configuration for Ring WebView IDE
# Configure your AI service API keys here

# Gemini AI API Key
# Get your key from: https://makersuite.google.com/app/apikey
GEMINI_API_KEY = "your_gemini_api_key_here"

# OpenAI API Key  
# Get your key from: https://platform.openai.com/api-keys
OPENAI_API_KEY = "your_openai_api_key_here"

# Claude API Key (Anthropic)
# Get your key from: https://console.anthropic.com/
CLAUDE_API_KEY = "your_claude_api_key_here"

# Default AI Provider
# Options: "gemini", "openai", "claude"
DEFAULT_AI_PROVIDER = "gemini"

# API Configuration
API_CONFIG = [
    :timeout = 30,
    :max_tokens = 1000,
    :temperature = 0.7,
    :retry_attempts = 3
]

# Feature Flags
FEATURES = [
    :ai_enabled = true,
    :file_operations = true,
    :code_execution = true,
    :git_integration = false,
    :debug_mode = true
]

# IDE Settings
IDE_SETTINGS = [
    :theme = "dark",
    :language = "ar",
    :auto_save = true,
    :auto_save_interval = 30,
    :show_line_numbers = true,
    :word_wrap = true,
    :font_size = 14,
    :tab_size = 4
]

# Project Templates
PROJECT_TEMPLATES = [
    [
        :name = "Basic Ring Project",
        :description = "مشروع Ring أساسي",
        :files = [
            ["main.ring", "# Main Ring Application\n\nsee \"Hello, World!\" + nl"],
            ["README.md", "# Ring Project\n\nBasic Ring application."]
        ]
    ],
    [
        :name = "Ring Web Application",
        :description = "تطبيق ويب Ring",
        :files = [
            ["main.ring", "# Ring Web Application\n\nload \"weblib.ring\"\n\n# Your web app code here"],
            ["views/index.html", "<!DOCTYPE html>\n<html>\n<head><title>Ring Web App</title></head>\n<body><h1>Welcome!</h1></body>\n</html>"],
            ["README.md", "# Ring Web Application\n\nWeb application built with Ring."]
        ]
    ],
    [
        :name = "Ring Desktop Application",
        :description = "تطبيق سطح مكتب Ring",
        :files = [
            ["main.ring", "# Ring Desktop Application\n\nload \"guilib.ring\"\n\n# Your desktop app code here"],
            ["README.md", "# Ring Desktop Application\n\nDesktop application built with Ring."]
        ]
    ]
]

# Code Snippets
CODE_SNIPPETS = [
    [
        :name = "Basic Function",
        :description = "دالة أساسية",
        :code = "func functionName(param1, param2)\n    # Function body\n    return result"
    ],
    [
        :name = "Class Definition",
        :description = "تعريف كلاس",
        :code = "class ClassName\n    # Public variables\n    cName = \"\"\n    \n    func init()\n        # Constructor\n    \n    func methodName()\n        # Method body\n    \n    private\n    \n    # Private variables and methods"
    ],
    [
        :name = "For Loop",
        :description = "حلقة for",
        :code = "for i = 1 to 10\n    # Loop body\n    see i + nl\nnext"
    ],
    [
        :name = "While Loop", 
        :description = "حلقة while",
        :code = "while condition\n    # Loop body\nend"
    ],
    [
        :name = "If Statement",
        :description = "جملة شرطية",
        :code = "if condition\n    # True block\nelseif condition2\n    # ElseIf block\nelse\n    # Else block\nok"
    ],
    [
        :name = "Try-Catch",
        :description = "معالجة الأخطاء",
        :code = "try\n    # Code that might throw an error\ncatch\n    see \"Error: \" + cCatchError + nl\ndone"
    ]
]

# File Extensions and Syntax Highlighting
FILE_TYPES = [
    [
        :extension = "ring",
        :language = "ring",
        :icon = "fas fa-file-code",
        :color = "#4CAF50"
    ],
    [
        :extension = "rh",
        :language = "ring",
        :icon = "fas fa-file-code",
        :color = "#4CAF50"
    ],
    [
        :extension = "md",
        :language = "markdown",
        :icon = "fab fa-markdown",
        :color = "#2196F3"
    ],
    [
        :extension = "txt",
        :language = "text",
        :icon = "fas fa-file-alt",
        :color = "#757575"
    ],
    [
        :extension = "json",
        :language = "json",
        :icon = "fas fa-file-code",
        :color = "#FF9800"
    ]
]

# Keyboard Shortcuts
KEYBOARD_SHORTCUTS = [
    [:key = "Ctrl+S", :action = "save", :description = "حفظ الملف"],
    [:key = "Ctrl+O", :action = "open", :description = "فتح ملف"],
    [:key = "Ctrl+N", :action = "new", :description = "ملف جديد"],
    [:key = "Ctrl+R", :action = "run", :description = "تشغيل الكود"],
    [:key = "F5", :action = "run", :description = "تشغيل الكود"],
    [:key = "Ctrl+F", :action = "find", :description = "بحث"],
    [:key = "Ctrl+H", :action = "replace", :description = "استبدال"],
    [:key = "Ctrl+Z", :action = "undo", :description = "تراجع"],
    [:key = "Ctrl+Y", :action = "redo", :description = "إعادة"],
    [:key = "Ctrl+/", :action = "comment", :description = "تعليق/إلغاء تعليق"]
]

# Help Topics
HELP_TOPICS = [
    [
        :title = "البدء السريع",
        :content = "مرحباً بك في Ring WebView IDE! هذا دليل سريع للبدء:\n\n1. إنشاء ملف جديد: Ctrl+N\n2. كتابة الكود في المحرر\n3. حفظ الملف: Ctrl+S\n4. تشغيل الكود: F5\n\nيمكنك أيضاً استخدام الوكيل الذكي للمساعدة في البرمجة."
    ],
    [
        :title = "الوكيل الذكي",
        :content = "الوكيل الذكي يمكنه مساعدتك في:\n\n• كتابة وتحليل الكود\n• حل المشاكل البرمجية\n• شرح المفاهيم\n• إنشاء أمثلة\n• مراجعة الكود\n\nاكتب سؤالك في نافذة الدردشة واضغط إرسال."
    ],
    [
        :title = "اختصارات لوحة المفاتيح",
        :content = "الاختصارات المفيدة:\n\n• Ctrl+S: حفظ\n• Ctrl+O: فتح\n• Ctrl+N: جديد\n• F5: تشغيل\n• Ctrl+F: بحث\n• Ctrl+Z: تراجع\n• Ctrl+/: تعليق"
    ]
]
