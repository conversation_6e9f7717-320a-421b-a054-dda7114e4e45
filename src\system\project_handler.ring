/*
=============================================================================
معالج المشاريع الشامل
Ring WebView IDE - Comprehensive Project Handler
=============================================================================
*/

load "../core/config.ring"

/*
=============================================================================
كلاس ComprehensiveProjectHandler - معالج المشاريع الشامل
Class: ComprehensiveProjectHandler
Description: Comprehensive project operations handler
=============================================================================
*/
class ComprehensiveProjectHandler
    
    # Private properties
    private
        oWebView = NULL
        cCurrentProject = ""
        aProjectFiles = []
        aProjectSettings = []
        
    # Public properties
    cProjectsDirectory = "projects/"
    cProjectExtension = ".ringproj"
    
    /*
    =========================================================================
    دالة البناء
    Function: init
    Description: Initialize project handler
    Input: oWebViewInstance (Object) - WebView instance
    =========================================================================
    */
    func init oWebViewInstance
        self.oWebView = oWebViewInstance
        
        # Create projects directory if not exists
        if not isdir(self.cProjectsDirectory)
            system("mkdir " + self.cProjectsDirectory)
        ok
        
        ? "تم تهيئة معالج المشاريع"
        
    /*
    =========================================================================
    دالة إنشاء مشروع جديد
    Function: createProject
    Description: Create new project
    Input: cProjectName (String), cProjectPath (String)
    Output: bSuccess (Boolean) - Creation success status
    =========================================================================
    */
    func createProject cProjectName, cProjectPath
        if cProjectName = ""
            raise("اسم المشروع مطلوب")
        ok
        
        if cProjectPath = ""
            cProjectPath = self.cProjectsDirectory + cProjectName
        ok
        
        try
            # Create project directory
            if not isdir(cProjectPath)
                system("mkdir " + cProjectPath)
            ok
            
            # Create project structure
            for cDir in aDefaultProjectStructure
                cFullPath = cProjectPath + "/" + cDir
                if right(cDir, 1) = "/"
                    if not isdir(cFullPath)
                        system("mkdir " + cFullPath)
                    ok
                else
                    if not fexists(cFullPath)
                        write(cFullPath, self.getDefaultFileContent(cDir))
                    ok
                ok
            next
            
            # Create project file
            aProjectData = [
                :name = cProjectName,
                :path = cProjectPath,
                :created = date() + " " + time(),
                :version = "1.0",
                :main_file = "main.ring",
                :files = [],
                :settings = [
                    :author = "",
                    :description = "",
                    :license = "MIT"
                ]
            ]
            
            cProjectFile = cProjectPath + "/" + cProjectName + self.cProjectExtension
            write(cProjectFile, list2json(aProjectData))
            
            self.cCurrentProject = cProjectFile
            self.loadProjectFiles()
            
            return true
            
        catch
            raise("خطأ في إنشاء المشروع: " + cCatchError)
        done
        
    /*
    =========================================================================
    دالة فتح مشروع
    Function: openProject
    Description: Open existing project
    Input: cProjectFile (String) - Project file path
    Output: bSuccess (Boolean) - Open success status
    =========================================================================
    */
    func openProject cProjectFile
        if not fexists(cProjectFile)
            raise("ملف المشروع غير موجود: " + cProjectFile)
        ok
        
        try
            cProjectData = read(cProjectFile)
            self.aProjectSettings = json2list(cProjectData)
            self.cCurrentProject = cProjectFile
            
            self.loadProjectFiles()
            
            return true
            
        catch
            raise("خطأ في فتح المشروع: " + cCatchError)
        done
        
    /*
    =========================================================================
    دالة حفظ المشروع
    Function: saveProject
    Description: Save current project
    Output: bSuccess (Boolean) - Save success status
    =========================================================================
    */
    func saveProject
        if self.cCurrentProject = ""
            raise("لا يوجد مشروع مفتوح للحفظ")
        ok
        
        try
            # Update project files list
            self.aProjectSettings[:files] = self.aProjectFiles
            self.aProjectSettings[:modified] = date() + " " + time()
            
            # Save project file
            cProjectData = list2json(self.aProjectSettings)
            write(self.cCurrentProject, cProjectData)
            
            return true
            
        catch
            raise("خطأ في حفظ المشروع: " + cCatchError)
        done
        
    /*
    =========================================================================
    دالة تحميل ملفات المشروع
    Function: loadProjectFiles
    Description: Load project files list
    =========================================================================
    */
    func loadProjectFiles
        if self.cCurrentProject = ""
            return
        ok
        
        cProjectPath = self.aProjectSettings[:path]
        self.aProjectFiles = []
        
        # Scan project directory recursively
        self.scanDirectory(cProjectPath, "")
        
    /*
    =========================================================================
    دالة مسح المجلد
    Function: scanDirectory
    Description: Scan directory for project files
    Input: cBasePath (String), cRelativePath (String)
    =========================================================================
    */
    func scanDirectory cBasePath, cRelativePath
        cFullPath = cBasePath
        if cRelativePath != ""
            cFullPath += "/" + cRelativePath
        ok
        
        try
            aItems = dir(cFullPath)
            
            for aItem in aItems
                cItemName = aItem[1]
                bIsDir = aItem[2]
                
                # Skip hidden files and directories
                if left(cItemName, 1) = "."
                    loop
                ok
                
                cItemPath = cRelativePath
                if cItemPath != ""
                    cItemPath += "/"
                ok
                cItemPath += cItemName
                
                if bIsDir
                    # Recursively scan subdirectory
                    self.scanDirectory(cBasePath, cItemPath)
                else
                    # Add file if supported
                    if isSupportedFile(cItemName)
                        self.aProjectFiles + cItemPath
                    ok
                ok
            next
            
        catch
            ? "تحذير: خطأ في مسح المجلد " + cFullPath + ": " + cCatchError
        done
        
    /*
    =========================================================================
    دالة الحصول على محتوى الملف الافتراضي
    Function: getDefaultFileContent
    Description: Get default content for project files
    Input: cFileName (String) - File name
    Output: cContent (String) - Default content
    =========================================================================
    */
    func getDefaultFileContent cFileName
        switch cFileName
            on "main.ring"
                return `# Ring WebView IDE Project
# Main application file

load "src/app.ring"

# Start application
oApp = new Application()
oApp.run()
`
            on "README.md"
                return `# Ring Project

## Description
This is a Ring programming language project created with Ring WebView IDE.

## Usage
Run the project with: \`ring main.ring\`

## Author
Created with Ring WebView IDE
`
            other
                return ""
        off
        
    /*
    =========================================================================
    دالة الحصول على معلومات المشروع
    Function: getProjectInfo
    Description: Get current project information
    Output: aInfo (List) - Project information
    =========================================================================
    */
    func getProjectInfo
        if self.cCurrentProject = ""
            return [:name = "", :path = "", :files_count = 0]
        ok
        
        return [
            :name = self.aProjectSettings[:name],
            :path = self.aProjectSettings[:path],
            :files_count = len(self.aProjectFiles),
            :created = self.aProjectSettings[:created],
            :modified = self.aProjectSettings[:modified],
            :main_file = self.aProjectSettings[:main_file]
        ]
        
    /*
    =========================================================================
    دالة الحصول على ملفات المشروع
    Function: getProjectFiles
    Description: Get project files list
    Output: aFiles (List) - Project files
    =========================================================================
    */
    func getProjectFiles
        return self.aProjectFiles
        
    /*
    =========================================================================
    دالة إضافة ملف للمشروع
    Function: addFileToProject
    Description: Add file to current project
    Input: cFilePath (String) - File path relative to project
    =========================================================================
    */
    func addFileToProject cFilePath
        if find(self.aProjectFiles, cFilePath) = 0
            self.aProjectFiles + cFilePath
        ok
        
    /*
    =========================================================================
    دالة إزالة ملف من المشروع
    Function: removeFileFromProject
    Description: Remove file from current project
    Input: cFilePath (String) - File path to remove
    =========================================================================
    */
    func removeFileFromProject cFilePath
        nIndex = find(self.aProjectFiles, cFilePath)
        if nIndex > 0
            del(self.aProjectFiles, nIndex)
        ok
